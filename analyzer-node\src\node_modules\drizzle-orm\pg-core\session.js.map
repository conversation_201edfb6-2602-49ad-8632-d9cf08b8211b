{"version": 3, "sources": ["../../src/pg-core/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { type Query, type SQL, sql } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport { PgDatabase } from './db.ts';\nimport type { PgDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class PgPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(protected query: Query) {}\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\tstatic readonly [entityKind]: string = 'PgPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface PgTransactionConfig {\n\tisolationLevel?: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n\taccessMode?: 'read only' | 'read write';\n\tdeferrable?: boolean;\n}\n\nexport abstract class PgSession<\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'PgSession';\n\n\tconstructor(protected dialect: PgDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t): PgPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.execute();\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T>;\n}\n\nexport abstract class PgTransaction<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends PgDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tsession: PgSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** @internal */\n\tgetTransactionConfigSQL(config: PgTransactionConfig): SQL {\n\t\tconst chunks: string[] = [];\n\t\tif (config.isolationLevel) {\n\t\t\tchunks.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\t\tif (config.accessMode) {\n\t\t\tchunks.push(config.accessMode);\n\t\t}\n\t\tif (typeof config.deferrable === 'boolean') {\n\t\t\tchunks.push(config.deferrable ? 'deferrable' : 'not deferrable');\n\t\t}\n\t\treturn sql.raw(chunks.join(' '));\n\t}\n\n\tsetTransaction(config: PgTransactionConfig): Promise<void> {\n\t\treturn this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PgQueryResultHKT {\n\treadonly $brand: 'PgQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type PgQueryResultKind<TKind extends PgQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,gCAAgC;AAGzC,SAA+B,WAAW;AAC1C,SAAS,cAAc;AACvB,SAAS,kBAAkB;AAUpB,MAAe,gBAAwE;AAAA,EAC7F,YAAsB,OAAc;AAAd;AAAA,EAAe;AAAA,EAErC,WAAkB;AACjB,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,UAAU,UAAmB,cAAiC;AAC7D,WAAO;AAAA,EACR;AAAA,EAEA,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AASD;AAQO,MAAe,UAIpB;AAAA,EAGD,YAAsB,SAAoB;AAApB;AAAA,EAAqB;AAAA,EAF3C,QAAiB,UAAU,IAAY;AAAA,EAYvC,QAAW,OAAwB;AAClC,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,YAAM,WAAW,OAAO,gBAAgB,wBAAwB,MAAM;AACrE,eAAO,KAAK;AAAA,UACX,KAAK,QAAQ,WAAW,KAAK;AAAA,UAC7B;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD,CAAC;AAED,aAAO,SAAS,QAAQ;AAAA,IACzB,CAAC;AAAA,EACF;AAAA,EAEA,IAAiB,OAA0B;AAC1C,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,IACD,EAAE,IAAI;AAAA,EACP;AAMD;AAEO,MAAe,sBAIZ,WAA+C;AAAA,EAGxD,YACC,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,SAAS,SAAS,MAAM;AAPpB;AAKS;AAAA,EAGpB;AAAA,EAbA,QAAiB,UAAU,IAAY;AAAA,EAevC,WAAkB;AACjB,UAAM,IAAI,yBAAyB;AAAA,EACpC;AAAA;AAAA,EAGA,wBAAwB,QAAkC;AACzD,UAAM,SAAmB,CAAC;AAC1B,QAAI,OAAO,gBAAgB;AAC1B,aAAO,KAAK,mBAAmB,OAAO,cAAc,EAAE;AAAA,IACvD;AACA,QAAI,OAAO,YAAY;AACtB,aAAO,KAAK,OAAO,UAAU;AAAA,IAC9B;AACA,QAAI,OAAO,OAAO,eAAe,WAAW;AAC3C,aAAO,KAAK,OAAO,aAAa,eAAe,gBAAgB;AAAA,IAChE;AACA,WAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC;AAAA,EAChC;AAAA,EAEA,eAAe,QAA4C;AAC1D,WAAO,KAAK,QAAQ,QAAQ,sBAAsB,KAAK,wBAAwB,MAAM,CAAC,EAAE;AAAA,EACzF;AAKD;", "names": []}