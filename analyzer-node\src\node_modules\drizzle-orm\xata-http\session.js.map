{"version": 3, "sources": ["../../src/xata-http/session.ts"], "sourcesContent": ["import type { SQLPluginResult, SQLQueryResult } from '@xata.io/client';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport type XataHttpClient = {\n\tsql: SQLPluginResult;\n};\n\nexport interface QueryResults<ArrayMode extends 'json' | 'array'> {\n\trowCount: number;\n\trows: ArrayMode extends 'array' ? any[][] : Record<string, any>[];\n\trowAsArray: ArrayMode extends 'array' ? true : false;\n}\n\nexport class XataHttpPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'XataHttpPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: XataHttpClient,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper(query);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, client, query, customResultMapper, joinsNotNullableMap } = this;\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn await client.sql<Record<string, any>>({ statement: query.sql, params });\n\t\t\t// return { rowCount: result.records.length, rows: result.records, rowAsArray: false };\n\t\t}\n\n\t\tconst { rows, warning } = await client.sql({ statement: query.sql, params, responseType: 'array' });\n\t\tif (warning) console.warn(warning);\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(rows as unknown[][])\n\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row as unknown[], joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.client.sql({ statement: this.query.sql, params, responseType: 'array' }).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.client.sql({ statement: this.query.sql, params }).then((result) => result.records);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface XataHttpSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class XataHttpSession<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends PgSession<\n\t\tXataHttpQueryResultHKT,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic readonly [entityKind]: string = 'XataHttpSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: XataHttpClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: XataHttpSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new XataHttpPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResults<'array'>> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.sql({ statement: query, params, responseType: 'array' });\n\n\t\treturn {\n\t\t\trowCount: result.rows.length,\n\t\t\trows: result.rows,\n\t\t\trowAsArray: true,\n\t\t};\n\t}\n\n\tasync queryObjects(query: string, params: unknown[]): Promise<QueryResults<'json'>> {\n\t\tconst result = await this.client.sql<Record<string, any>>({ statement: query, params });\n\n\t\treturn {\n\t\t\trowCount: result.records.length,\n\t\t\trows: result.records,\n\t\t\trowAsArray: false,\n\t\t};\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: XataTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t\t_config: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tthrow new Error('No transactions support in Xata Http driver');\n\t}\n}\n\nexport class XataTransaction<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends PgTransaction<\n\t\tXataHttpQueryResultHKT,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic readonly [entityKind]: string = 'XataHttpTransaction';\n\n\toverride async transaction<T>(_transaction: (tx: XataTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tthrow new Error('No transactions support in Xata Http driver');\n\t}\n}\n\nexport interface XataHttpQueryResultHKT extends PgQueryResultHKT {\n\ttype: SQLQueryResult<this['row']>;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAG9B,SAAS,iBAAiB,iBAAiB;AAE3C,SAAS,wBAAoC;AAC7C,SAAS,oBAAoB;AAYtB,MAAM,8BAA6D,gBAAmB;AAAA,EAG5F,YACS,QACR,OACQ,QACA,QACA,wBACA,oBACP;AACD,UAAM,KAAK;AAPH;AAEA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAXA,QAAiB,UAAU,IAAY;AAAA,EAavC,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AAEpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,QAAQ,OAAO,oBAAoB,oBAAoB,IAAI;AAE3E,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,MAAM,OAAO,IAAyB,EAAE,WAAW,MAAM,KAAK,OAAO,CAAC;AAAA,IAE9E;AAEA,UAAM,EAAE,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,EAAE,WAAW,MAAM,KAAK,QAAQ,cAAc,QAAQ,CAAC;AAClG,QAAI;AAAS,cAAQ,KAAK,OAAO;AAEjC,WAAO,qBACJ,mBAAmB,IAAmB,IACtC,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAkB,mBAAmB,CAAC;AAAA,EAChG;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,IAAI,EAAE,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,QAAQ,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAClH;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,IAAI,EAAE,WAAW,KAAK,MAAM,KAAK,OAAO,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,OAAO;AAAA,EAC9F;AAAA;AAAA,EAGA,wBAAwB;AACvB,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,wBACJ,UAKT;AAAA,EAKC,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAAmD;AAC7E,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,IAAI,EAAE,WAAW,OAAO,QAAQ,cAAc,QAAQ,CAAC;AAExF,WAAO;AAAA,MACN,UAAU,OAAO,KAAK;AAAA,MACtB,MAAM,OAAO;AAAA,MACb,YAAY;AAAA,IACb;AAAA,EACD;AAAA,EAEA,MAAM,aAAa,OAAe,QAAkD;AACnF,UAAM,SAAS,MAAM,KAAK,OAAO,IAAyB,EAAE,WAAW,OAAO,OAAO,CAAC;AAEtF,WAAO;AAAA,MACN,UAAU,OAAO,QAAQ;AAAA,MACzB,MAAM,OAAO;AAAA,MACb,YAAY;AAAA,IACb;AAAA,EACD;AAAA,EAEA,MAAe,YACd,cAEA,UAA+B,CAAC,GACnB;AACb,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;AAEO,MAAM,wBACJ,cAKT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,MAAe,YAAe,cAAqF;AAClH,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;", "names": []}