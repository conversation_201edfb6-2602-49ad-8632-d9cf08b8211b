{"version": 3, "sources": ["../../src/pglite/session.ts"], "sourcesContent": ["import type { PGlite, QueryOptions, Results, Row, Transaction } from '@electric-sql/pglite';\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nimport { types } from '@electric-sql/pglite';\n\nexport type PgliteClient = PGlite;\n\nexport class PglitePreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'PglitePreparedQuery';\n\n\tprivate rawQueryConfig: QueryOptions;\n\tprivate queryConfig: QueryOptions;\n\n\tconstructor(\n\t\tprivate client: PgliteClient | Transaction,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQueryConfig = {\n\t\t\trowMode: 'object',\n\t\t\tparsers: {\n\t\t\t\t[types.TIMESTAMP]: (value) => value,\n\t\t\t\t[types.TIMESTAMPTZ]: (value) => value,\n\t\t\t\t[types.INTERVAL]: (value) => value,\n\t\t\t\t[types.DATE]: (value) => value,\n\t\t\t},\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\trowMode: 'array',\n\t\t\tparsers: {\n\t\t\t\t[types.TIMESTAMP]: (value) => value,\n\t\t\t\t[types.TIMESTAMPTZ]: (value) => value,\n\t\t\t\t[types.INTERVAL]: (value) => value,\n\t\t\t\t[types.DATE]: (value) => value,\n\t\t\t},\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\tconst { fields, rawQueryConfig, client, queryConfig, joinsNotNullableMap, customResultMapper, queryString } = this;\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client.query<any[]>(queryString, params, rawQueryConfig);\n\t\t}\n\n\t\tconst result = await client.query<any[][]>(queryString, params, queryConfig);\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(result.rows)\n\t\t\t: result.rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.queryString, params);\n\t\treturn this.client.query(this.queryString, params, this.rawQueryConfig).then((result) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface PgliteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PgliteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<PgliteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgliteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: PgliteClient | Transaction,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: PgliteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new PglitePreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: PgliteTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\treturn (this.client as PgliteClient).transaction(async (client) => {\n\t\t\tconst session = new PgliteSession<TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.options,\n\t\t\t);\n\t\t\tconst tx = new PgliteTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\t\tif (config) {\n\t\t\t\tawait tx.setTransaction(config);\n\t\t\t}\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport class PgliteTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<PgliteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgliteTransaction';\n\n\toverride async transaction<T>(transaction: (tx: PgliteTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new PgliteTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface PgliteQueryResultHKT extends PgQueryResultHKT {\n\ttype: Results<Assume<this['row'], Row>>;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAsB,kBAAkB;AAExC,SAAS,qBAAqB;AAG9B,SAAS,iBAAiB,iBAAiB;AAE3C,SAAS,kBAA8B,WAAW;AAClD,SAAsB,oBAAoB;AAE1C,SAAS,aAAa;AAIf,MAAM,4BAA2D,gBAAmB;AAAA,EAM1F,YACS,QACA,aACA,QACA,QACA,QACR,MACQ,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAT1B;AACA;AACA;AACA;AACA;AAEA;AACA;AAGR,SAAK,iBAAiB;AAAA,MACrB,SAAS;AAAA,MACT,SAAS;AAAA,QACR,CAAC,MAAM,SAAS,GAAG,CAAC,UAAU;AAAA,QAC9B,CAAC,MAAM,WAAW,GAAG,CAAC,UAAU;AAAA,QAChC,CAAC,MAAM,QAAQ,GAAG,CAAC,UAAU;AAAA,QAC7B,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU;AAAA,MAC1B;AAAA,IACD;AACA,SAAK,cAAc;AAAA,MAClB,SAAS;AAAA,MACT,SAAS;AAAA,QACR,CAAC,MAAM,SAAS,GAAG,CAAC,UAAU;AAAA,QAC9B,CAAC,MAAM,WAAW,GAAG,CAAC,UAAU;AAAA,QAChC,CAAC,MAAM,QAAQ,GAAG,CAAC,UAAU;AAAA,QAC7B,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AAAA,EAlCA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAiCR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,UAAM,EAAE,QAAQ,gBAAgB,QAAQ,aAAa,qBAAqB,oBAAoB,YAAY,IAAI;AAE9G,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAa,aAAa,QAAQ,cAAc;AAAA,IAC/D;AAEA,UAAM,SAAS,MAAM,OAAO,MAAe,aAAa,QAAQ,WAAW;AAE3E,WAAO,qBACJ,mBAAmB,OAAO,IAAI,IAC9B,OAAO,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EAC1F;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,QAAQ,KAAK,cAAc,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EACrG;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,sBAGH,UAAsD;AAAA,EAK/D,YACS,QACR,SACQ,QACA,UAAgC,CAAC,GACxC;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,WAAQ,KAAK,OAAwB,YAAY,OAAO,WAAW;AAClE,YAAM,UAAU,IAAI;AAAA,QACnB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AACA,YAAM,KAAK,IAAI,kBAAwC,KAAK,SAAS,SAAS,KAAK,MAAM;AACzF,UAAI,QAAQ;AACX,cAAM,GAAG,eAAe,MAAM;AAAA,MAC/B;AACA,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,0BAGH,cAA0D;AAAA,EACnE,QAAiB,UAAU,IAAY;AAAA,EAEvC,MAAe,YAAe,aAAsF;AACnH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": []}