{"version": 3, "sources": ["../../src/neon-http/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport { type SQL, sql } from '~/sql/sql.ts';\nimport type { NeonHttpDatabase } from './driver.ts';\n\n/**\n * This function reads migrationFolder and execute each unapplied migration and mark it as executed in database\n *\n * NOTE: The Neon HTTP driver does not support transactions. This means that if any part of a migration fails,\n * no rollback will be executed. Currently, you will need to handle unsuccessful migration yourself.\n * @param db - drizzle db instance\n * @param config - path to migration folder generated by drizzle-kit\n */\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: NeonHttpDatabase<TSchema>,\n\tconfig: string | MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tconst migrationsTable = typeof config === 'string'\n\t\t? '__drizzle_migrations'\n\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\tconst migrationsSchema = typeof config === 'string' ? 'drizzle' : config.migrationsSchema ?? 'drizzle';\n\tconst migrationTableCreate = sql`\n\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\n\t\t\tid SERIAL PRIMARY KEY,\n\t\t\thash text NOT NULL,\n\t\t\tcreated_at bigint\n\t\t)\n\t`;\n\tawait db.session.execute(sql`CREATE SCHEMA IF NOT EXISTS ${sql.identifier(migrationsSchema)}`);\n\tawait db.session.execute(migrationTableCreate);\n\n\tconst dbMigrations = await db.session.all<{ id: number; hash: string; created_at: string }>(\n\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsSchema)}.${\n\t\t\tsql.identifier(migrationsTable)\n\t\t} order by created_at desc limit 1`,\n\t);\n\n\tconst lastDbMigration = dbMigrations[0];\n\tconst rowsToInsert: SQL[] = [];\n\tfor await (const migration of migrations) {\n\t\tif (\n\t\t\t!lastDbMigration\n\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t) {\n\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\tawait db.session.execute(sql.raw(stmt));\n\t\t\t}\n\n\t\t\trowsToInsert.push(\n\t\t\t\tsql`insert into ${sql.identifier(migrationsSchema)}.${\n\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t);\n\t\t}\n\t}\n\n\tfor await (const rowToInsert of rowsToInsert) {\n\t\tawait db.session.execute(rowToInsert);\n\t}\n}\n"], "mappings": "AACA,SAAS,0BAA0B;AACnC,SAAmB,WAAW;AAW9B,eAAsB,QACrB,IACA,QACC;AACD,QAAM,aAAa,mBAAmB,MAAM;AAC5C,QAAM,kBAAkB,OAAO,WAAW,WACvC,yBACA,OAAO,mBAAmB;AAC7B,QAAM,mBAAmB,OAAO,WAAW,WAAW,YAAY,OAAO,oBAAoB;AAC7F,QAAM,uBAAuB;AAAA,+BACC,IAAI,WAAW,gBAAgB,CAAC,IAAI,IAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAMjG,QAAM,GAAG,QAAQ,QAAQ,kCAAkC,IAAI,WAAW,gBAAgB,CAAC,EAAE;AAC7F,QAAM,GAAG,QAAQ,QAAQ,oBAAoB;AAE7C,QAAM,eAAe,MAAM,GAAG,QAAQ;AAAA,IACrC,uCAAuC,IAAI,WAAW,gBAAgB,CAAC,IACtE,IAAI,WAAW,eAAe,CAC/B;AAAA,EACD;AAEA,QAAM,kBAAkB,aAAa,CAAC;AACtC,QAAM,eAAsB,CAAC;AAC7B,mBAAiB,aAAa,YAAY;AACzC,QACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,iBAAW,QAAQ,UAAU,KAAK;AACjC,cAAM,GAAG,QAAQ,QAAQ,IAAI,IAAI,IAAI,CAAC;AAAA,MACvC;AAEA,mBAAa;AAAA,QACZ,kBAAkB,IAAI,WAAW,gBAAgB,CAAC,IACjD,IAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,MAC5E;AAAA,IACD;AAAA,EACD;AAEA,mBAAiB,eAAe,cAAc;AAC7C,UAAM,GAAG,QAAQ,QAAQ,WAAW;AAAA,EACrC;AACD;", "names": []}