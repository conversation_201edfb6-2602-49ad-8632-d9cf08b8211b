import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
interface Blob {}
declare class EKSAuth extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: EKSAuth.Types.ClientConfiguration)
  config: Config & EKSAuth.Types.ClientConfiguration;
  /**
   * The Amazon EKS Auth API and the AssumeRoleForPodIdentity action are only used by the EKS Pod Identity Agent. We recommend that applications use the Amazon Web Services SDKs to connect to Amazon Web Services services; if credentials from an EKS Pod Identity association are available in the pod, the latest versions of the SDKs use them automatically.
   */
  assumeRoleForPodIdentity(params: EKSAuth.Types.AssumeRoleForPodIdentityRequest, callback?: (err: AWSError, data: EKSAuth.Types.AssumeRoleForPodIdentityResponse) => void): Request<EKSAuth.Types.AssumeRoleForPodIdentityResponse, AWSError>;
  /**
   * The Amazon EKS Auth API and the AssumeRoleForPodIdentity action are only used by the EKS Pod Identity Agent. We recommend that applications use the Amazon Web Services SDKs to connect to Amazon Web Services services; if credentials from an EKS Pod Identity association are available in the pod, the latest versions of the SDKs use them automatically.
   */
  assumeRoleForPodIdentity(callback?: (err: AWSError, data: EKSAuth.Types.AssumeRoleForPodIdentityResponse) => void): Request<EKSAuth.Types.AssumeRoleForPodIdentityResponse, AWSError>;
}
declare namespace EKSAuth {
  export interface AssumeRoleForPodIdentityRequest {
    /**
     * The name of the cluster for the request.
     */
    clusterName: ClusterName;
    /**
     * The token of the Kubernetes service account for the pod.
     */
    token: JwtToken;
  }
  export interface AssumeRoleForPodIdentityResponse {
    /**
     * The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.
     */
    subject: Subject;
    /**
     * The identity that is allowed to use the credentials. This value is always pods.eks.amazonaws.com.
     */
    audience: String;
    /**
     * The Amazon Resource Name (ARN) and ID of the EKS Pod Identity association.
     */
    podIdentityAssociation: PodIdentityAssociation;
    /**
     * An object with the permanent IAM role identity and the temporary session name. The ARN of the IAM role that the temporary credentials authenticate to. The session name of the temporary session requested to STS. The value is a unique identifier that contains the role ID, a colon (:), and the role session name of the role that is being assumed. The role ID is generated by IAM when the role is created. The role session name part of the value follows this format: eks-clustername-podname-random UUID  
     */
    assumedRoleUser: AssumedRoleUser;
    /**
     * The Amazon Web Services Signature Version 4 type of temporary credentials.
     */
    credentials: Credentials;
  }
  export interface AssumedRoleUser {
    /**
     * The ARN of the IAM role that the temporary credentials authenticate to.
     */
    arn: String;
    /**
     * The session name of the temporary session requested to STS. The value is a unique identifier that contains the role ID, a colon (:), and the role session name of the role that is being assumed. The role ID is generated by IAM when the role is created. The role session name part of the value follows this format: eks-clustername-podname-random UUID  
     */
    assumeRoleId: String;
  }
  export type ClusterName = string;
  export interface Credentials {
    /**
     * The token that applications inside the pods must pass to any service API to use the temporary credentials.
     */
    sessionToken: String;
    /**
     * The secret access key that applications inside the pods use to sign requests.
     */
    secretAccessKey: String;
    /**
     * The access key ID that identifies the temporary security credentials.
     */
    accessKeyId: String;
    /**
     * The Unix epoch timestamp in seconds when the current credentials expire.
     */
    expiration: Timestamp;
  }
  export type JwtToken = string;
  export interface PodIdentityAssociation {
    /**
     * The Amazon Resource Name (ARN) of the EKS Pod Identity association.
     */
    associationArn: String;
    /**
     * The ID of the association.
     */
    associationId: String;
  }
  export type String = string;
  export interface Subject {
    /**
     * The name of the Kubernetes namespace inside the cluster to create the association in. The service account and the pods that use the service account must be in this namespace.
     */
    namespace: String;
    /**
     * The name of the Kubernetes service account inside the cluster to associate the IAM credentials with.
     */
    serviceAccount: String;
  }
  export type Timestamp = Date;
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2023-11-26"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the EKSAuth client.
   */
  export import Types = EKSAuth;
}
export = EKSAuth;
