{"version": 3, "sources": ["../src/selection-proxy.ts"], "sourcesContent": ["import { ColumnAliasProxyHandler, TableAliasProxyHandler } from './alias.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { SQL, View } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class SelectionProxyHandler<T extends Subquery | Record<string, unknown> | View>\n\timplements ProxyHandler<Subquery | Record<string, unknown> | View>\n{\n\tstatic readonly [entityKind]: string = 'SelectionProxyHandler';\n\n\tprivate config: {\n\t\t/**\n\t\t * Table alias for the columns\n\t\t */\n\t\talias?: string;\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL.Aliased` and it's not a selection field (from a subquery)\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `alias` - return the field alias\n\t\t */\n\t\tsqlAliasedBehavior: 'sql' | 'alias';\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL` and it doesn't have an alias declared\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `error` - return a DrizzleTypeError on type level and throw an error on runtime\n\t\t */\n\t\tsqlBehavior: 'sql' | 'error';\n\n\t\t/**\n\t\t * Whether to replace the original name of the column with the alias\n\t\t * Should be set to `true` for views creation\n\t\t * @default false\n\t\t */\n\t\treplaceOriginalName?: boolean;\n\t};\n\n\tconstructor(config: SelectionProxyHandler<T>['config']) {\n\t\tthis.config = { ...config };\n\t}\n\n\tget(subquery: T, prop: string | symbol): any {\n\t\tif (prop === '_') {\n\t\t\treturn {\n\t\t\t\t...subquery['_' as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as Subquery)._.selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...subquery[ViewBaseConfig as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as View)[ViewBaseConfig].selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (typeof prop === 'symbol') {\n\t\t\treturn subquery[prop as keyof typeof subquery];\n\t\t}\n\n\t\tconst columns = is(subquery, Subquery)\n\t\t\t? subquery._.selectedFields\n\t\t\t: is(subquery, View)\n\t\t\t? subquery[ViewBaseConfig].selectedFields\n\t\t\t: subquery;\n\t\tconst value: unknown = columns[prop as keyof typeof columns];\n\n\t\tif (is(value, SQL.Aliased)) {\n\t\t\t// Never return the underlying SQL expression for a field previously selected in a subquery\n\t\t\tif (this.config.sqlAliasedBehavior === 'sql' && !value.isSelectionField) {\n\t\t\t\treturn value.sql;\n\t\t\t}\n\n\t\t\tconst newValue = value.clone();\n\t\t\tnewValue.isSelectionField = true;\n\t\t\treturn newValue;\n\t\t}\n\n\t\tif (is(value, SQL)) {\n\t\t\tif (this.config.sqlBehavior === 'sql') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t`You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`,\n\t\t\t);\n\t\t}\n\n\t\tif (is(value, Column)) {\n\t\t\tif (this.config.alias) {\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tvalue,\n\t\t\t\t\tnew ColumnAliasProxyHandler(\n\t\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\t\tvalue.table,\n\t\t\t\t\t\t\tnew TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false),\n\t\t\t\t\t\t),\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tif (typeof value !== 'object' || value === null) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn new Proxy(value, new SelectionProxyHandler(this.config));\n\t}\n}\n"], "mappings": "AAAA,SAAS,yBAAyB,8BAA8B;AAChE,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAC/B,SAAS,KAAK,YAAY;AAC1B,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;AAExB,MAAM,sBAEb;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EA8BR,YAAY,QAA4C;AACvD,SAAK,SAAS,EAAE,GAAG,OAAO;AAAA,EAC3B;AAAA,EAEA,IAAI,UAAa,MAA4B;AAC5C,QAAI,SAAS,KAAK;AACjB,aAAO;AAAA,QACN,GAAG,SAAS,GAA4B;AAAA,QACxC,gBAAgB,IAAI;AAAA,UAClB,SAAsB,EAAE;AAAA,UACzB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,SAAS,gBAAgB;AAC5B,aAAO;AAAA,QACN,GAAG,SAAS,cAAuC;AAAA,QACnD,gBAAgB,IAAI;AAAA,UAClB,SAAkB,cAAc,EAAE;AAAA,UACnC;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,SAAS,IAA6B;AAAA,IAC9C;AAEA,UAAM,UAAU,GAAG,UAAU,QAAQ,IAClC,SAAS,EAAE,iBACX,GAAG,UAAU,IAAI,IACjB,SAAS,cAAc,EAAE,iBACzB;AACH,UAAM,QAAiB,QAAQ,IAA4B;AAE3D,QAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAE3B,UAAI,KAAK,OAAO,uBAAuB,SAAS,CAAC,MAAM,kBAAkB;AACxE,eAAO,MAAM;AAAA,MACd;AAEA,YAAM,WAAW,MAAM,MAAM;AAC7B,eAAS,mBAAmB;AAC5B,aAAO;AAAA,IACR;AAEA,QAAI,GAAG,OAAO,GAAG,GAAG;AACnB,UAAI,KAAK,OAAO,gBAAgB,OAAO;AACtC,eAAO;AAAA,MACR;AAEA,YAAM,IAAI;AAAA,QACT,2BAA2B,IAAI;AAAA,MAChC;AAAA,IACD;AAEA,QAAI,GAAG,OAAO,MAAM,GAAG;AACtB,UAAI,KAAK,OAAO,OAAO;AACtB,eAAO,IAAI;AAAA,UACV;AAAA,UACA,IAAI;AAAA,YACH,IAAI;AAAA,cACH,MAAM;AAAA,cACN,IAAI,uBAAuB,KAAK,OAAO,OAAO,KAAK,OAAO,uBAAuB,KAAK;AAAA,YACvF;AAAA,UACD;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,aAAO;AAAA,IACR;AAEA,WAAO,IAAI,MAAM,OAAO,IAAI,sBAAsB,KAAK,MAAM,CAAC;AAAA,EAC/D;AACD;", "names": []}