{"version": 3, "sources": ["../../../src/pg-core/columns/macaddr.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgMacaddrBuilderInitial<TName extends string> = PgMacaddrBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgMacaddr';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgMacaddrBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgMacaddr'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgMacaddrBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgMacaddr');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgMacaddr<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgMacaddr<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgMacaddr<T extends ColumnBaseConfig<'string', 'PgMacaddr'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgMacaddr';\n\n\tgetSQLType(): string {\n\t\treturn 'macaddr';\n\t}\n}\n\nexport function macaddr<TName extends string>(name: TName): PgMacaddrBuilderInitial<TName> {\n\treturn new PgMacaddrBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;AAYnC,MAAM,yBAAmF,gBAAmB;AAAA,EAClH,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,WAAW;AAAA,EAClC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAqE,SAAY;AAAA,EAC7F,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,QAA8B,MAA6C;AAC1F,SAAO,IAAI,iBAAiB,IAAI;AACjC;", "names": []}