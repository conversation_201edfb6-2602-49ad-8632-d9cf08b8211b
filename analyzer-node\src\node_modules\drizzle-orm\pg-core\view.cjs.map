{"version": 3, "sources": ["../../src/pg-core/view.ts"], "sourcesContent": ["import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { PgColumn, PgColumnBuilderBase } from './columns/common.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport { pgTable } from './table.ts';\nimport { PgViewBase } from './view-base.ts';\nimport { PgViewConfig } from './view-common.ts';\n\nexport interface ViewWithConfig {\n\tcheckOption: 'local' | 'cascaded';\n\tsecurityBarrier: boolean;\n\tsecurityInvoker: boolean;\n}\n\nexport class DefaultViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'PgDefaultViewBuilderCore';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: ViewWithConfig;\n\t} = {};\n\n\twith(config: ViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n}\n\nexport class ViewBuilder<TName extends string = string> extends DefaultViewBuilderCore<{ name: TName }> {\n\tstatic readonly [entityKind]: string = 'PgViewBuilder';\n\n\tas<TSelectedFields extends SelectedFields>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): PgViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as PgViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, PgColumnBuilderBase> = Record<string, PgColumnBuilderBase>,\n> extends DefaultViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic readonly [entityKind]: string = 'PgManualViewBuilder';\n\n\tprivate columns: Record<string, PgColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(pgTable(name, columns));\n\t}\n\n\texisting(): PgViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n\n\tas(query: SQL): PgViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n}\n\nexport interface PgMaterializedViewWithConfig {\n\t[Key: string]: string | number | boolean | SQL;\n}\n\nexport class MaterializedViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'PgMaterializedViewBuilderCore';\n\n\tdeclare _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: PgMaterializedViewWithConfig;\n\t\tusing?: string;\n\t\ttablespace?: string;\n\t\twithNoData?: boolean;\n\t} = {};\n\n\tusing(using: string): this {\n\t\tthis.config.using = using;\n\t\treturn this;\n\t}\n\n\twith(config: PgMaterializedViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n\n\ttablespace(tablespace: string): this {\n\t\tthis.config.tablespace = tablespace;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n}\n\nexport class MaterializedViewBuilder<TName extends string = string>\n\textends MaterializedViewBuilderCore<{ name: TName }>\n{\n\tstatic readonly [entityKind]: string = 'PgMaterializedViewBuilder';\n\n\tas<TSelectedFields extends SelectedFields>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): PgMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: {\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as PgMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>>;\n\t}\n}\n\nexport class ManualMaterializedViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, PgColumnBuilderBase> = Record<string, PgColumnBuilderBase>,\n> extends MaterializedViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic readonly [entityKind]: string = 'PgManualMaterializedViewBuilder';\n\n\tprivate columns: Record<string, PgColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(pgTable(name, columns));\n\t}\n\n\texisting(): PgMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n\n\tas(query: SQL): PgMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n}\n\nexport class PgView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends PgViewBase<TName, TExisting, TSelectedFields> {\n\tstatic readonly [entityKind]: string = 'PgView';\n\n\t[PgViewConfig]: {\n\t\twith?: ViewWithConfig;\n\t} | undefined;\n\n\tconstructor({ pgConfig, config }: {\n\t\tpgConfig: {\n\t\t\twith?: ViewWithConfig;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: SelectedFields;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tif (pgConfig) {\n\t\t\tthis[PgViewConfig] = {\n\t\t\t\twith: pgConfig.with,\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport type PgViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = PgView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\nexport const PgMaterializedViewConfig = Symbol.for('drizzle:PgMaterializedViewConfig');\n\nexport class PgMaterializedView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends PgViewBase<TName, TExisting, TSelectedFields> {\n\tstatic readonly [entityKind]: string = 'PgMaterializedView';\n\n\treadonly [PgMaterializedViewConfig]: {\n\t\treadonly with?: PgMaterializedViewWithConfig;\n\t\treadonly using?: string;\n\t\treadonly tablespace?: string;\n\t\treadonly withNoData?: boolean;\n\t} | undefined;\n\n\tconstructor({ pgConfig, config }: {\n\t\tpgConfig: {\n\t\t\twith: PgMaterializedViewWithConfig | undefined;\n\t\t\tusing: string | undefined;\n\t\t\ttablespace: string | undefined;\n\t\t\twithNoData: boolean | undefined;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: SelectedFields;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tthis[PgMaterializedViewConfig] = {\n\t\t\twith: pgConfig?.with,\n\t\t\tusing: pgConfig?.using,\n\t\t\ttablespace: pgConfig?.tablespace,\n\t\t\twithNoData: pgConfig?.withNoData,\n\t\t};\n\t}\n}\n\nexport type PgMaterializedViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = PgMaterializedView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\n/** @internal */\nexport function pgViewWithSchema(\n\tname: string,\n\tselection: Record<string, PgColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): ViewBuilder | ManualViewBuilder {\n\tif (selection) {\n\t\treturn new ManualViewBuilder(name, selection, schema);\n\t}\n\treturn new ViewBuilder(name, schema);\n}\n\n/** @internal */\nexport function pgMaterializedViewWithSchema(\n\tname: string,\n\tselection: Record<string, PgColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\tif (selection) {\n\t\treturn new ManualMaterializedViewBuilder(name, selection, schema);\n\t}\n\treturn new MaterializedViewBuilder(name, schema);\n}\n\nexport function pgView<TName extends string>(name: TName): ViewBuilder<TName>;\nexport function pgView<TName extends string, TColumns extends Record<string, PgColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualViewBuilder<TName, TColumns>;\nexport function pgView(name: string, columns?: Record<string, PgColumnBuilderBase>): ViewBuilder | ManualViewBuilder {\n\treturn pgViewWithSchema(name, columns, undefined);\n}\n\nexport function pgMaterializedView<TName extends string>(name: TName): MaterializedViewBuilder<TName>;\nexport function pgMaterializedView<TName extends string, TColumns extends Record<string, PgColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualMaterializedViewBuilder<TName, TColumns>;\nexport function pgMaterializedView(\n\tname: string,\n\tcolumns?: Record<string, PgColumnBuilderBase>,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\treturn pgMaterializedViewWithSchema(name, columns, undefined);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAG3B,6BAAsC;AAEtC,mBAAgC;AAEhC,2BAA6B;AAE7B,mBAAwB;AACxB,uBAA2B;AAC3B,yBAA6B;AAQtB,MAAM,uBAA4E;AAAA,EAQxF,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,wBAAU,IAAY;AAAA,EAY7B,SAEN,CAAC;AAAA,EAEL,KAAK,QAA8B;AAClC,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AACD;AAEO,MAAM,oBAAmD,uBAAwC;AAAA,EACvG,QAAiB,wBAAU,IAAY;AAAA,EAEvC,GACC,IACuF;AACvF,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,kCAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,6CAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,OAAO;AAAA,QACV,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,uBAA2D;AAAA,EACpE,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,cAAU,kCAAgB,sBAAQ,MAAM,OAAO,CAAC;AAAA,EACtD;AAAA,EAEA,WAAkF;AACjF,WAAO,IAAI;AAAA,MACV,IAAI,OAAO;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAAoF;AACtF,WAAO,IAAI;AAAA,MACV,IAAI,OAAO;AAAA,QACV,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAMO,MAAM,4BAAiF;AAAA,EAQ7F,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,wBAAU,IAAY;AAAA,EAY7B,SAKN,CAAC;AAAA,EAEL,MAAM,OAAqB;AAC1B,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAEA,KAAK,QAA4C;AAChD,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEA,WAAW,YAA0B;AACpC,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,aAAmB;AAClB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAM,gCACJ,4BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,GACC,IACmG;AACnG,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,kCAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,6CAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,mBAAmB;AAAA,QACtB,UAAU;AAAA,UACT,MAAM,KAAK,OAAO;AAAA,UAClB,OAAO,KAAK,OAAO;AAAA,UACnB,YAAY,KAAK,OAAO;AAAA,UACxB,YAAY,KAAK,OAAO;AAAA,QACzB;AAAA,QACA,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,sCAGH,4BAAgE;AAAA,EACzE,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,cAAU,kCAAgB,sBAAQ,MAAM,OAAO,CAAC;AAAA,EACtD;AAAA,EAEA,WAA8F;AAC7F,WAAO,IAAI;AAAA,MACV,IAAI,mBAAmB;AAAA,QACtB,UAAU;AAAA,QACV,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAAgG;AAClG,WAAO,IAAI;AAAA,MACV,IAAI,mBAAmB;AAAA,QACtB,UAAU;AAAA,QACV,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,MAAM,eAIH,4BAA8C;AAAA,EACvD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,CAAC,+BAAY;AAAA,EAIb,YAAY,EAAE,UAAU,OAAO,GAU5B;AACF,UAAM,MAAM;AACZ,QAAI,UAAU;AACb,WAAK,+BAAY,IAAI;AAAA,QACpB,MAAM,SAAS;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AACD;AAQO,MAAM,2BAA2B,OAAO,IAAI,kCAAkC;AAE9E,MAAM,2BAIH,4BAA8C;AAAA,EACvD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,CAAU,wBAAwB;AAAA,EAOlC,YAAY,EAAE,UAAU,OAAO,GAa5B;AACF,UAAM,MAAM;AACZ,SAAK,wBAAwB,IAAI;AAAA,MAChC,MAAM,UAAU;AAAA,MAChB,OAAO,UAAU;AAAA,MACjB,YAAY,UAAU;AAAA,MACtB,YAAY,UAAU;AAAA,IACvB;AAAA,EACD;AACD;AASO,SAAS,iBACf,MACA,WACA,QACkC;AAClC,MAAI,WAAW;AACd,WAAO,IAAI,kBAAkB,MAAM,WAAW,MAAM;AAAA,EACrD;AACA,SAAO,IAAI,YAAY,MAAM,MAAM;AACpC;AAGO,SAAS,6BACf,MACA,WACA,QAC0D;AAC1D,MAAI,WAAW;AACd,WAAO,IAAI,8BAA8B,MAAM,WAAW,MAAM;AAAA,EACjE;AACA,SAAO,IAAI,wBAAwB,MAAM,MAAM;AAChD;AAOO,SAAS,OAAO,MAAc,SAAgF;AACpH,SAAO,iBAAiB,MAAM,SAAS,MAAS;AACjD;AAOO,SAAS,mBACf,MACA,SAC0D;AAC1D,SAAO,6BAA6B,MAAM,SAAS,MAAS;AAC7D;", "names": []}