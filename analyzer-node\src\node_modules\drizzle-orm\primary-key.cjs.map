{"version": 3, "sources": ["../src/primary-key.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { AnyColumn } from './column.ts';\nimport type { Table } from './table.ts';\n\nexport abstract class PrimaryKey {\n\tstatic readonly [entityKind]: string = 'PrimaryKey';\n\n\tdeclare protected $brand: 'PrimaryKey';\n\n\tconstructor(readonly table: Table, readonly columns: AnyColumn[]) {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAIpB,MAAe,WAAW;AAAA,EAKhC,YAAqB,OAAuB,SAAsB;AAA7C;AAAuB;AAAA,EAAuB;AAAA,EAJnE,QAAiB,wBAAU,IAAY;AAKxC;", "names": []}