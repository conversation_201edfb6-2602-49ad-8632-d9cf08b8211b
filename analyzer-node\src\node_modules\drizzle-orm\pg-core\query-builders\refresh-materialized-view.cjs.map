{"version": 3, "sources": ["../../../src/pg-core/query-builders/refresh-materialized-view.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgMaterializedView } from '~/pg-core/view.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends\n\t\tQueryPromise<PgQueryResultKind<TQueryResult, never>>,\n\t\tRunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>,\n\t\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: PgQueryResultKind<TQueryResult, never>;\n\t};\n}\n\nexport class PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends QueryPromise<PgQueryResultKind<TQueryResult, never>>\n\timplements RunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>, SQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgRefreshMaterializedView';\n\n\tprivate config: {\n\t\tview: PgMaterializedView;\n\t\tconcurrently?: boolean;\n\t\twithNoData?: boolean;\n\t};\n\n\tconstructor(\n\t\tview: PgMaterializedView,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { view };\n\t}\n\n\tconcurrently(): this {\n\t\tif (this.config.withNoData !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tif (this.config.concurrently !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRefreshMaterializedViewQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), undefined, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn this._prepare(name);\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAU3B,2BAA6B;AAG7B,qBAAuB;AAehB,MAAM,kCACJ,kCAET;AAAA,EASC,YACC,MACQ,SACA,SACP;AACD,UAAM;AAHE;AACA;AAGR,SAAK,SAAS,EAAE,KAAK;AAAA,EACtB;AAAA,EAfA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAeR,eAAqB;AACpB,QAAI,KAAK,OAAO,eAAe,QAAW;AACzC,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,SAAK,OAAO,eAAe;AAC3B,WAAO;AAAA,EACR;AAAA,EAEA,aAAmB;AAClB,QAAI,KAAK,OAAO,iBAAiB,QAAW;AAC3C,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,kCAAkC,KAAK,MAAM;AAAA,EAClE;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAIP;AACD,WAAO,sBAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,QAAW,MAAM,IAAI;AAAA,IAC/F,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAIN;AACD,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEA,UAAkD,CAAC,sBAAsB;AACxE,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AACD;", "names": []}