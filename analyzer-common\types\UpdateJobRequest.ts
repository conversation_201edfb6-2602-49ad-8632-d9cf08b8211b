import { QuestionVideoForCreation } from "./CreateJobRequest";

export interface UpdateJobRequest {
    warmupQuestions: string[];
    jobId: string;
    A46: string[];
    A47: string[];
    A48: string[];
    A49: string[];
    openQuestions: string[];
    title: string;
    description: string;
    customerId: string;
    language: string;
    accountId: string;
    myInterviewJobId: string;
    introVideo: string;
    questionVideos: QuestionVideoForCreation[];
    computedTraits: string[];
}
