import { LanguageVariation } from "./FamiliesWithTraitsForDisplayMap";
import { QuestionsLanguageVariations } from "./QuestionOptions";

export interface QuestionVideo {
    customerName: string;
    language: string;
    videoLink: string;
    gender: string | null;
    trait: string;
    question: string;
    questionVideoId: string;
}

export interface QuestionVideosResponse {
    videos: QuestionVideo[];
}
