{"version": 3, "sources": ["../src/operations.ts"], "sourcesContent": ["import type { AnyColumn, Column } from './column.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { Table } from './table.ts';\n\nexport type RequiredKeyOnly<TK<PERSON> extends string, T extends Column> = T extends AnyColumn<{\n\tnotNull: true;\n\thasDefault: false;\n}> ? TKey\n\t: never;\n\nexport type NotGenerated<TK<PERSON> extends string, T extends Column> = T extends AnyColumn<{\n\tgenerated: undefined;\n}> ? TKey\n\t: never;\n\nexport type OptionalKeyOnly<\n\tTKey extends string,\n\tT extends Column,\n> = TK<PERSON> extends RequiredKeyOnly<TKey, T> ? never\n\t: T<PERSON><PERSON> extends NotGenerated<TKey, T> ? TKey\n\t: T['_']['generated'] extends object ? T['_']['generated']['type'] extends 'byDefault' ? TKey : never\n\t: never;\n\nexport type SelectedFieldsFlat<TColumn extends Column> = Record<\n\tstring,\n\tTColumn | SQL | SQL.Aliased\n>;\n\nexport type SelectedFieldsFlatFull<TColumn extends Column> = Record<\n\tstring,\n\tTColumn | SQL | SQL.Aliased\n>;\n\nexport type SelectedFields<TColumn extends Column, TTable extends Table> = Record<\n\tstring,\n\tSelectedFieldsFlat<TColumn>[string] | TTable | SelectedFieldsFlat<TColumn>\n>;\n\nexport type SelectedFieldsOrdered<TColumn extends Column> = {\n\tpath: string[];\n\tfield: TColumn | SQL | SQL.Aliased;\n}[];\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}