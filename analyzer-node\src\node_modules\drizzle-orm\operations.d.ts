import type { AnyColumn, Column } from "./column.js";
import type { SQL } from "./sql/sql.js";
import type { Table } from "./table.js";
export type RequiredKeyOnly<TK<PERSON> extends string, T extends Column> = T extends AnyColumn<{
    notNull: true;
    hasDefault: false;
}> ? TKey : never;
export type NotGenerated<TKey extends string, T extends Column> = T extends AnyColumn<{
    generated: undefined;
}> ? TKey : never;
export type OptionalKeyOnly<TKey extends string, T extends Column> = TK<PERSON> extends RequiredKeyOnly<TKey, T> ? never : T<PERSON><PERSON> extends NotGenerated<TKey, T> ? TKey : T['_']['generated'] extends object ? T['_']['generated']['type'] extends 'byDefault' ? TKey : never : never;
export type SelectedFieldsFlat<TColumn extends Column> = Record<string, TColumn | SQL | SQL.Aliased>;
export type SelectedFieldsFlatFull<TColumn extends Column> = Record<string, TColumn | SQL | SQL.Aliased>;
export type SelectedFields<TColumn extends Column, TTable extends Table> = Record<string, SelectedFieldsFlat<TColumn>[string] | TTable | SelectedFieldsFlat<TColumn>>;
export type SelectedFieldsOrdered<TColumn extends Column> = {
    path: string[];
    field: TColumn | SQL | SQL.Aliased;
}[];
