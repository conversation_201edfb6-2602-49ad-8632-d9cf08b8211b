{"pagination": {"DescribeActionTargets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ActionTargets"}, "DescribeProducts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Products"}, "DescribeStandards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Standards"}, "DescribeStandardsControls": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Controls"}, "GetEnabledStandards": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StandardsSubscriptions"}, "GetFindingHistory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Records"}, "GetFindings": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Findings"}, "GetInsights": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Insights"}, "ListConfigurationPolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationPolicySummaries"}, "ListConfigurationPolicyAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ConfigurationPolicyAssociationSummaries"}, "ListEnabledProductsForImport": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ProductSubscriptions"}, "ListFindingAggregators": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "FindingAggregators"}, "ListInvitations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Invitations"}, "ListMembers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Members"}, "ListOrganizationAdminAccounts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AdminAccounts"}, "ListSecurityControlDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SecurityControlDefinitions"}, "ListStandardsControlAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StandardsControlAssociationSummaries"}}}