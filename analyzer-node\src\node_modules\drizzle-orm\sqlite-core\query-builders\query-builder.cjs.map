{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { WithSubqueryWithSelection } from '~/sqlite-core/subquery.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport { SQLiteSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteQueryBuilder';\n\n\tprivate dialect: SQLiteSyncDialect | undefined;\n\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst queryBuilder = this;\n\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(queryBuilder);\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct };\n\t}\n\n\tselect(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\treturn new SQLiteSelectBuilder({ fields: fields ?? undefined, session: undefined, dialect: this.getDialect() });\n\t}\n\n\tselectDistinct(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\treturn new SQLiteSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new SQLiteSyncDialect();\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,6BAAsC;AAEtC,qBAAkC;AAElC,sBAA6B;AAC7B,oBAAoC;AAG7B,MAAM,aAAa;AAAA,EACzB,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAER,MAA6B,OAAe;AAC3C,UAAM,eAAe;AAErB,WAAO;AAAA,MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,YAAY;AAAA,QACrB;AAEA,eAAO,IAAI;AAAA,UACV,IAAI,6BAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;AAAA,UACnF,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,QACvF;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAMb,aAAS,OACR,QACkE;AAClE,aAAO,IAAI,kCAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAMA,aAAS,eACR,QACkE;AAClE,aAAO,IAAI,kCAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,eAAe;AAAA,EACjC;AAAA,EAMA,OACC,QACkE;AAClE,WAAO,IAAI,kCAAoB,EAAE,QAAQ,UAAU,QAAW,SAAS,QAAW,SAAS,KAAK,WAAW,EAAE,CAAC;AAAA,EAC/G;AAAA,EAMA,eACC,QACkE;AAClE,WAAO,IAAI,kCAAoB;AAAA,MAC9B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,iCAAkB;AAAA,IACtC;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}