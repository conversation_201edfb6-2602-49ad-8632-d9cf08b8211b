{"version": 3, "sources": ["../../../src/pg-core/query-builders/update.ts"], "sourcesContent": ["import type { GetColumnData } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { mapUpdateSet, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFields, SelectedFieldsOrdered } from './select.types.ts';\n\nexport interface PgUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: PgTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgUpdateSetSource<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL;\n\t}\n\t& {};\n\nexport class PgUpdateBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tset(values: PgUpdateSetSource<TTable>): PgUpdateBase<TTable, TQueryResult> {\n\t\treturn new PgUpdateBase<TTable, TQueryResult>(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t);\n\t}\n}\n\nexport type PgUpdateWithout<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['returning'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type PgUpdateReturningAll<T extends AnyPgUpdate, TDynamic extends boolean> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdateReturning<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdatePrepare<T extends AnyPgUpdate> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgUpdateDynamic<T extends AnyPgUpdate> = PgUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type PgUpdate<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgUpdateBase<TTable, TQueryResult, TReturning, true, never>;\n\ntype AnyPgUpdate = PgUpdateBase<any, any, any, any, any>;\n\nexport interface PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgUpdate';\n\n\tprivate config: PgUpdateConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList };\n\t}\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): PgUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFields = this.config.table[Table.Symbol.Columns],\n\t): PgUpdateWithout<AnyPgUpdate, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgUpdatePrepare<this> {\n\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t}\n\n\tprepare(name: string): PgUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues);\n\t};\n\n\t$dynamic(): PgUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAW3B,2BAA6B;AAI7B,mBAAsB;AACtB,mBAAkE;AAoB3D,MAAM,gBAA+E;AAAA,EAO3F,YACS,OACA,SACA,SACA,UACP;AAJO;AACA;AACA;AACA;AAAA,EACN;AAAA,EAXH,QAAiB,wBAAU,IAAY;AAAA,EAavC,IAAI,QAAuE;AAC1E,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,UACL,2BAAa,KAAK,OAAO,MAAM;AAAA,MAC/B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAwFO,MAAM,qBAQH,kCAIV;AAAA,EAKC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,SAAS;AAAA,EACtC;AAAA,EAbA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8CR,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA4BA,UACC,SAAyB,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO,GACT;AACtD,SAAK,OAAO,gBAAY,kCAA8B,MAAM;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAsC;AAC9C,WAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;AAAA,EAC3G;AAAA,EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,EACjD;AAAA,EAEA,WAAkC;AACjC,WAAO;AAAA,EACR;AACD;", "names": []}