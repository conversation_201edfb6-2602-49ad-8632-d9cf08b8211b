{"version": 3, "sources": ["../../src/mysql-core/subquery.ts"], "sourcesContent": ["import type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport type { Subquery, WithSubquery } from '~/subquery.ts';\n\nexport type SubqueryWithSelection<\n\tTSelection extends ColumnsSelection,\n\t<PERSON><PERSON><PERSON><PERSON> extends string,\n> =\n\t& Subquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'mysql'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'mysql'>;\n\nexport type WithSubqueryWithSelection<\n\tTSelection extends ColumnsSelection,\n\t<PERSON><PERSON><PERSON><PERSON> extends string,\n> =\n\t& WithSubquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'mysql'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'mysql'>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}