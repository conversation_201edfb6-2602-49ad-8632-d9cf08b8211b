{"version": 3, "sources": ["../../../src/pg-core/columns/line.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport type { Equal } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgLineBuilderInitial<TName extends string> = PgLineBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgLine';\n\tdata: [number, number, number];\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgLineBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgLine'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgLineBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'array', 'PgLine');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgLineTuple<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgLineTuple<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgLineTuple<T extends ColumnBaseConfig<'array', 'PgLine'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgLine';\n\n\tgetSQLType(): string {\n\t\treturn 'line';\n\t}\n\n\toverride mapFromDriverValue(value: string): [number, number, number] {\n\t\tconst [a, b, c] = value.slice(1, -1).split(',');\n\t\treturn [Number.parseFloat(a!), Number.parseFloat(b!), Number.parseFloat(c!)];\n\t}\n\n\toverride mapToDriverValue(value: [number, number, number]): string {\n\t\treturn `{${value[0]},${value[1]},${value[2]}}`;\n\t}\n}\n\nexport type PgLineABCBuilderInitial<TName extends string> = PgLineABCBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'PgLineABC';\n\tdata: { a: number; b: number; c: number };\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgLineABCBuilder<T extends ColumnBuilderBaseConfig<'json', 'PgLineABC'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgLineABCBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'PgLineABC');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgLineABC<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgLineABC<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgLineABC<T extends ColumnBaseConfig<'json', 'PgLineABC'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgLineABC';\n\n\tgetSQLType(): string {\n\t\treturn 'line';\n\t}\n\n\toverride mapFromDriverValue(value: string): { a: number; b: number; c: number } {\n\t\tconst [a, b, c] = value.slice(1, -1).split(',');\n\t\treturn { a: Number.parseFloat(a!), b: Number.parseFloat(b!), c: Number.parseFloat(c!) };\n\t}\n\n\toverride mapToDriverValue(value: { a: number; b: number; c: number }): string {\n\t\treturn `{${value.a},${value.b},${value.c}}`;\n\t}\n}\n\nexport interface PgLineTypeConfig<T extends 'tuple' | 'abc' = 'tuple' | 'abc'> {\n\tmode?: T;\n}\n\nexport function line<TName extends string, TMode extends PgLineTypeConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgLineTypeConfig<TMode>,\n): Equal<TMode, 'abc'> extends true ? PgLineABCBuilderInitial<TName>\n\t: PgLineBuilderInitial<TName>;\nexport function line(name: string, config?: PgLineTypeConfig) {\n\tif (!config?.mode || config.mode === 'tuple') {\n\t\treturn new PgLineBuilder(name);\n\t}\n\n\treturn new PgLineABCBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAI3B,SAAS,UAAU,uBAAuB;AAYnC,MAAM,sBAA4E,gBAAmB;AAAA,EAC3G,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,SAAS,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,oBAAmE,SAAY;AAAA,EAC3F,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAyC;AACpE,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,WAAO,CAAC,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,GAAG,OAAO,WAAW,CAAE,CAAC;AAAA,EAC5E;AAAA,EAES,iBAAiB,OAAyC;AAClE,WAAO,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EAC5C;AACD;AAYO,MAAM,yBAAiF,gBAAmB;AAAA,EAChH,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,WAAW;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,kBAAmE,SAAY;AAAA,EAC3F,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAoD;AAC/E,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC9C,WAAO,EAAE,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,GAAG,GAAG,OAAO,WAAW,CAAE,EAAE;AAAA,EACvF;AAAA,EAES,iBAAiB,OAAoD;AAC7E,WAAO,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EACzC;AACD;AAWO,SAAS,KAAK,MAAc,QAA2B;AAC7D,MAAI,CAAC,QAAQ,QAAQ,OAAO,SAAS,SAAS;AAC7C,WAAO,IAAI,cAAc,IAAI;AAAA,EAC9B;AAEA,SAAO,IAAI,iBAAiB,IAAI;AACjC;", "names": []}