{"version": 3, "sources": ["../../../src/mysql-core/query-builders/query.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { Query, QueryWithTypings, SQL } from '~/sql/sql.ts';\nimport type { KnownKeysOnly } from '~/utils.ts';\nimport type { MySqlDialect } from '../dialect.ts';\nimport type {\n\tMode,\n\tMySqlPreparedQueryConfig,\n\tMySqlSession,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n} from '../session.ts';\nimport type { MySqlTable } from '../table.ts';\n\nexport class RelationalQueryBuilder<\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTSchema extends TablesRelationalConfig,\n\tTFields extends TableRelationalConfig,\n> {\n\tstatic readonly [entityKind]: string = 'MySqlRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TSchema,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: MySqlTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: MySqlDialect,\n\t\tprivate session: MySqlSession,\n\t\tprivate mode: Mode,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): MySqlRelationalQuery<TPreparedQueryHKT, BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn new MySqlRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t'many',\n\t\t\tthis.mode,\n\t\t);\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): MySqlRelationalQuery<TPreparedQueryHKT, BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn new MySqlRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t'first',\n\t\t\tthis.mode,\n\t\t);\n\t}\n}\n\nexport class MySqlRelationalQuery<\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTResult,\n> extends QueryPromise<TResult> {\n\tstatic readonly [entityKind]: string = 'MySqlRelationalQuery';\n\n\tdeclare protected $brand: 'MySqlRelationalQuery';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: MySqlTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: MySqlDialect,\n\t\tprivate session: MySqlSession,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tprivate queryMode: 'many' | 'first',\n\t\tprivate mode?: Mode,\n\t) {\n\t\tsuper();\n\t}\n\n\tprepare() {\n\t\tconst { query, builtQuery } = this._toSQL();\n\t\treturn this.session.prepareQuery(\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\t(rawRows) => {\n\t\t\t\tconst rows = rawRows.map((row) => mapRelationalRow(this.schema, this.tableConfig, row, query.selection));\n\t\t\t\tif (this.queryMode === 'first') {\n\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t}\n\t\t\t\treturn rows as TResult;\n\t\t\t},\n\t\t) as PreparedQueryKind<TPreparedQueryHKT, MySqlPreparedQueryConfig & { execute: TResult }, true>;\n\t}\n\n\tprivate _getQuery() {\n\t\tconst query = this.mode === 'planetscale'\n\t\t\t? this.dialect.buildRelationalQueryWithoutLateralSubqueries({\n\t\t\t\tfullSchema: this.fullSchema,\n\t\t\t\tschema: this.schema,\n\t\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\t\ttable: this.table,\n\t\t\t\ttableConfig: this.tableConfig,\n\t\t\t\tqueryConfig: this.config,\n\t\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t\t})\n\t\t\t: this.dialect.buildRelationalQuery({\n\t\t\t\tfullSchema: this.fullSchema,\n\t\t\t\tschema: this.schema,\n\t\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\t\ttable: this.table,\n\t\t\t\ttableConfig: this.tableConfig,\n\t\t\t\tqueryConfig: this.config,\n\t\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t\t});\n\t\treturn query;\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this._getQuery();\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { builtQuery, query };\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this._getQuery().sql as SQL;\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\toverride execute(): Promise<TResult> {\n\t\treturn this.prepare().execute();\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B;AAAA,EAIC;AAAA,OAGM;AAaA,MAAM,uBAIX;AAAA,EAGD,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACA,MACP;AARO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACN;AAAA,EAXH,QAAiB,UAAU,IAAY;AAAA,EAavC,SACC,QACyF;AACzF,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAU,SAAyC,CAAC;AAAA,MACpD;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAEA,UACC,QACsG;AACtG,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;AAAA,MAC3F;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,6BAGH,aAAsB;AAAA,EAK/B,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACA,QACA,WACA,MACP;AACD,UAAM;AAXE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAjBA,QAAiB,UAAU,IAAY;AAAA,EAmBvC,UAAU;AACT,UAAM,EAAE,OAAO,WAAW,IAAI,KAAK,OAAO;AAC1C,WAAO,KAAK,QAAQ;AAAA,MACnB;AAAA,MACA;AAAA,MACA,CAAC,YAAY;AACZ,cAAM,OAAO,QAAQ,IAAI,CAAC,QAAQ,iBAAiB,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,SAAS,CAAC;AACvG,YAAI,KAAK,cAAc,SAAS;AAC/B,iBAAO,KAAK,CAAC;AAAA,QACd;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA,EAEQ,YAAY;AACnB,UAAM,QAAQ,KAAK,SAAS,gBACzB,KAAK,QAAQ,6CAA6C;AAAA,MAC3D,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK,YAAY;AAAA,IAC9B,CAAC,IACC,KAAK,QAAQ,qBAAqB;AAAA,MACnC,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK,YAAY;AAAA,IAC9B,CAAC;AACF,WAAO;AAAA,EACR;AAAA,EAEQ,SAA8E;AACrF,UAAM,QAAQ,KAAK,UAAU;AAE7B,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM,GAAU;AAE3D,WAAO,EAAE,YAAY,MAAM;AAAA,EAC5B;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,UAAU,EAAE;AAAA,EACzB;AAAA,EAEA,QAAe;AACd,WAAO,KAAK,OAAO,EAAE;AAAA,EACtB;AAAA,EAES,UAA4B;AACpC,WAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,EAC/B;AACD;", "names": []}