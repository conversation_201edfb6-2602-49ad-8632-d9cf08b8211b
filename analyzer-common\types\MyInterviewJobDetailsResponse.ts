export interface MyInterviewJobDetailsResponse {
    statusCode: number;
    errorCode: number;
    callId: string;
    statusReason: string;
    time: string;
    data: {
        config: {
            hideQuestions: boolean;
            practice: boolean;
        };
        creatorDetails: {
            name: string;
            email: string;
        };
        candidate_experience: {
            flow: any[]; // Adjust to a more specific type if available
        };
        title: string;
        apiKey: string;
        company: string;
        companySlug: string;
        slug: string;
        company_id: string;
        experience: string;
        jobDescription: string;
        job_id: string;
        kanban: {
            id: string;
            columnOrder: number;
            columnLabel: string;
            color: string;
            columnType: string;
            automations: {
                action_id: string;
                enabled: boolean;
                label: string;
                actionType: string;
                actionExtraBehaviour: string;
                requiresAttention: boolean;
                actionConfig?: {
                    to: string;
                    subject: string;
                    template: string;
                };
            }[];
            createdAt: string;
        }[];
        logo: string;
        questions: {
            answerType: string;
            numOfRetakes: number;
            partDuration: number;
            question: string;
            thinkingTime: number;
        }[];
        skills: any[]; // Adjust to a more specific type if available
        status: string;
        intelligenceIDs: any[]; // Adjust to a more specific type if available
        introVideo: string;
        categories: any[]; // Adjust to a more specific type if available
        type: string;
        language: string;
        creatorId: string;
        jobInputFields: any[]; // Adjust to a more specific type if available
        practiceQuestions: any[]; // Adjust to a more specific type if available
        permissions: any[]; // Adjust to a more specific type if available
        updatedAt: string;
        createdAt: string;
        intelligenceSurvey: any[]; // Adjust to a more specific type if available
        additionalFields: any[]; // Adjust to a more specific type if available
        scorecards: any[]; // Adjust to a more specific type if available
        benefits: any[]; // Adjust to a more specific type if available
        direct_link: string;
    };
}
