export interface MyInterviewActionConfig {
  to: string;
  subject: string;
  template: string;
}

export interface MyInterviewAutomation {
  action_id: string;
  enabled: boolean;
  label: string;
  actionType: string;
  actionExtraBehaviour: string;
  requiresAttention: boolean;
  actionConfig?: MyInterviewActionConfig | null; // Optional or null
}

export interface MyInterviewKanbanColumn {
  id: string;
  columnOrder: number;
  columnLabel: string;
  color: string;
  columnType: string;
  automations: MyInterviewAutomation[];
  createdAt: string;
}

export interface MyInterviewConfig {
  hideQuestions: boolean;
  practice: boolean;
}

export interface MyInterviewQuestion {
  answerType: string;
  numOfRetakes: number;
  partDuration: number;
  question: string;
  thinkingTime: number;
}

export interface MyInterviewCreatorDetails {
  name: string;
  email: string;
}

export interface MyInterviewCandidateExperience {
  flow: any[]; // Replace `any` with a more specific type if known
}

export interface MyInterviewData {
  title: string;
  apiKey: string;
  company: string;
  companySlug: string;
  slug: string;
  company_id: string;
  config: MyInterviewConfig;
  experience: string;
  jobDescription: string;
  job_id: string;
  kanban: MyInterviewKanbanColumn[];
  logo: string;
  questions: MyInterviewQuestion[];
  status: string;
  intelligenceIDs: any[]; // Replace `any` with a more specific type if known
  introVideo: string;
  api_created: boolean;
  type: string;
  creatorId?: string | null;
  creatorDetails?: MyInterviewCreatorDetails | null;
  jobInputFields: any[]; // Replace `any` with a more specific type if known
  candidate_experience: MyInterviewCandidateExperience;
  practiceQuestions: any[]; // Replace `any` with a more specific type if known
  updatedAt: string;
  createdAt: string;
  intelligenceSurvey: any[]; // Replace `any` with a more specific type if known
  additionalFields: any[]; // Replace `any` with a more specific type if known
  scorecards: any[]; // Replace `any` with a more specific type if known
  benefits: any[]; // Replace `any` with a more specific type if known
  direct_link: string;
  update_url: string;
}

export interface MyInterviewResponseModel {
  statusCode: number;
  errorCode: number;
  callId: string;
  statusReason: string;
  time: string;
  data: MyInterviewData;
}
