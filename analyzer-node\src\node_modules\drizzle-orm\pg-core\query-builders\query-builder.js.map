{"version": 3, "sources": ["../../../src/pg-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { PgColumn } from '../columns/index.ts';\nimport type { WithSubqueryWithSelection } from '../subquery.ts';\nimport { PgSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'PgQueryBuilder';\n\n\tprivate dialect: PgDialect | undefined;\n\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst queryBuilder = this;\n\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(queryBuilder);\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): PgSelectBuilder<SelectedFields | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): PgSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): PgSelectBuilder<SelectedFields | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new PgDialect();\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAE1B,SAAS,6BAA6B;AAEtC,SAAS,oBAAoB;AAG7B,SAAS,uBAAuB;AAGzB,MAAM,aAAa;AAAA,EACzB,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAER,MAA6B,OAAe;AAC3C,UAAM,eAAe;AAErB,WAAO;AAAA,MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,YAAY;AAAA,QACrB;AAEA,eAAO,IAAI;AAAA,UACV,IAAI,aAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;AAAA,UACnF,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,QACvF;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAIb,aAAS,OACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAIA,aAAS,eAAe,QAA4E;AACnG,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAOA,aAAS,iBACR,IACA,QACoD;AACpD,aAAO,IAAI,gBAAgB;AAAA,QAC1B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU,EAAE,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,gBAAgB,iBAAiB;AAAA,EACnD;AAAA,EAIA,OAA0C,QAAoE;AAC7G,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAIA,eAAe,QAAsE;AACpF,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA,EAOA,iBACC,IACA,QAC8C;AAC9C,WAAO,IAAI,gBAAgB;AAAA,MAC1B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU,EAAE,GAAG;AAAA,IAChB,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,UAAU;AAAA,IAC9B;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}