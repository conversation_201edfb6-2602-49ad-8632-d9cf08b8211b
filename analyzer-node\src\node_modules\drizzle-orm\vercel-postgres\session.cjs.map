{"version": 3, "sources": ["../../src/vercel-postgres/session.ts"], "sourcesContent": ["import {\n\ttype QueryArrayConfig,\n\ttype QueryConfig,\n\ttype QueryResult,\n\ttype QueryResultRow,\n\ttype VercelClient,\n\tVercelPool,\n\ttype VercelPoolClient,\n} from '@vercel/postgres';\nimport { entityKind } from '~/entity.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport { type PgDialect, PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport type VercelPgClient = VercelPool | VercelClient | VercelPoolClient;\n\nexport class VercelPgPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'VercelPgPreparedQuery';\n\n\tprivate rawQuery: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: VercelPgClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQuery = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\n\t\tconst { fields, rawQuery, client, queryConfig: query, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client.query(rawQuery, params);\n\t\t}\n\n\t\tconst { rows } = await client.query(query, params);\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\t\treturn this.client.query(this.rawQuery, params).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQuery.text, params);\n\t\treturn this.client.query(this.queryConfig, params).then((result) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface VercelPgSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class VercelPgSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<VercelPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'VercelPgSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: VercelPgClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: VercelPgSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new VercelPgPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tname,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResult> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.query({\n\t\t\trowMode: 'array',\n\t\t\ttext: query,\n\t\t\tvalues: params,\n\t\t});\n\t\treturn result;\n\t}\n\n\tasync queryObjects<T extends QueryResultRow>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<QueryResult<T>> {\n\t\treturn this.client.query<T>(query, params);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: VercelPgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst session = this.client instanceof VercelPool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new VercelPgSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new VercelPgTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin${config ? sql` ${tx.getTransactionConfigSQL(config)}` : undefined}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof VercelPool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as VercelPoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class VercelPgTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<VercelPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'VercelPgTransaction';\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: VercelPgTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new VercelPgTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface VercelPgQueryResultHKT extends PgQueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAQO;AACP,oBAA2B;AAC3B,oBAAwC;AACxC,qBAA8C;AAG9C,qBAA2C;AAE3C,iBAAkD;AAClD,mBAA0C;AAInC,MAAM,8BAA6D,+BAAmB;AAAA,EAM5F,YACS,QACR,aACQ,QACA,QACA,QACR,MACQ,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAT1B;AAEA;AACA;AACA;AAEA;AACA;AAGR,SAAK,WAAW;AAAA,MACf;AAAA,MACA,MAAM;AAAA,IACP;AACA,SAAK,cAAc;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EAzBA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAwBR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAE/C,UAAM,EAAE,QAAQ,UAAU,QAAQ,aAAa,OAAO,qBAAqB,mBAAmB,IAAI;AAClG,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAM,UAAU,MAAM;AAAA,IACrC;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM;AAEjD,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAC/C,WAAO,KAAK,OAAO,MAAM,KAAK,UAAU,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAC7E;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,SAAS,MAAM,MAAM;AAC/C,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,wBAGH,yBAAwD;AAAA,EAKjE,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAAyC;AACnE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,MAAM;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,aACL,OACA,QAC0B;AAC1B,WAAO,KAAK,OAAO,MAAS,OAAO,MAAM;AAAA,EAC1C;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,UAAU,KAAK,kBAAkB,6BACpC,IAAI,gBAAgB,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IACxF;AACH,UAAM,KAAK,IAAI,oBAA0C,KAAK,SAAS,SAAS,KAAK,MAAM;AAC3F,UAAM,GAAG,QAAQ,sBAAW,SAAS,kBAAO,GAAG,wBAAwB,MAAM,CAAC,KAAK,MAAS,EAAE;AAC9F,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,sBAAW;AAC5B,aAAO;AAAA,IACR,SAAS,OAAO;AACf,YAAM,GAAG,QAAQ,wBAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,KAAK,kBAAkB,4BAAY;AACtC,QAAC,QAAQ,OAA4B,QAAQ;AAAA,MAC9C;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,4BAGH,6BAA4D;AAAA,EACrE,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": []}