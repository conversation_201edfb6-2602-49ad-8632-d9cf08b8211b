{"version": 3, "sources": ["../../../src/pg-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgVarcharBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgVarcharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgVarchar';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tgenerated: undefined;\n}>;\n\nexport class PgVarcharBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgVarchar'>> extends PgColumnBuilder<\n\tT,\n\t{ length: number | undefined; enumValues: T['enumValues'] }\n> {\n\tstatic readonly [entityKind]: string = 'PgVarcharBuilder';\n\n\tconstructor(name: string, config: PgVarcharConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgVarchar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVarchar<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgVarchar<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgVarchar<T extends ColumnBaseConfig<'string', 'PgVarchar'>>\n\textends PgColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'PgVarchar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface PgVarcharConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tlength?: number;\n\tenum?: TEnum;\n}\n\nexport function varchar<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: PgVarcharConfig<T | Writable<T>> = {},\n): PgVarcharBuilderInitial<TName, Writable<T>> {\n\treturn new PgVarcharBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAA0C;AAYnC,MAAM,yBAAmF,8BAG9F;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,QAA0C;AACnE,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,SAAS,KAAK,OAAO;AAAA,EACZ,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;AAAA,EACtE;AACD;AAOO,SAAS,QACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,MAAM;AACzC;", "names": []}