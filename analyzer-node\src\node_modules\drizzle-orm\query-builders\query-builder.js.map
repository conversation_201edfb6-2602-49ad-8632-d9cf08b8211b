{"version": 3, "sources": ["../../src/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/sql/index.ts';\n\nexport abstract class TypedQueryBuilder<TSelection, TResult = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'TypedQueryBuilder';\n\n\tdeclare _: {\n\t\tselectedFields: TSelection;\n\t\tresult: TResult;\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): TSelection {\n\t\treturn this._.selectedFields;\n\t}\n\n\tabstract getSQL(): SQL;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAGpB,MAAe,kBAAuE;AAAA,EAC5F,QAAiB,UAAU,IAAY;AAAA;AAAA,EAQvC,oBAAgC;AAC/B,WAAO,KAAK,EAAE;AAAA,EACf;AAGD;", "names": []}