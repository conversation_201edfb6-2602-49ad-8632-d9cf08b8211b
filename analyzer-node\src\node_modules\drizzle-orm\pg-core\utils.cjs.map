{"version": 3, "sources": ["../../src/pg-core/utils.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport { Table } from '~/table.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { type Check, CheckBuilder } from './checks.ts';\nimport type { AnyPgColumn } from './columns/index.ts';\nimport { type ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport { type PrimaryKey, PrimaryKeyBuilder } from './primary-keys.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\nimport { PgViewConfig } from './view-common.ts';\nimport { type PgMaterializedView, PgMaterializedViewConfig, type PgView } from './view.ts';\n\nexport function getTableConfig<TTable extends PgTable>(table: TTable) {\n\tconst columns = Object.values(table[Table.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst checks: Check[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst foreignKeys: ForeignKey[] = Object.values(table[PgTable.Symbol.InlineForeignKeys]);\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst name = table[Table.Symbol.Name];\n\tconst schema = table[Table.Symbol.Schema];\n\n\tconst extraConfigBuilder = table[PgTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[Table.Symbol.ExtraConfigColumns]);\n\t\tfor (const builder of Object.values(extraConfig)) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, CheckBuilder)) {\n\t\t\t\tchecks.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, ForeignKeyBuilder)) {\n\t\t\t\tforeignKeys.push(builder.build(table));\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tforeignKeys,\n\t\tchecks,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t\tschema,\n\t};\n}\n\nexport function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: PgView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[PgViewConfig],\n\t};\n}\n\nexport function getMaterializedViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: PgMaterializedView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[PgMaterializedViewConfig],\n\t};\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TForeignTableName }> };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAmB;AACnB,mBAAwB;AACxB,IAAAA,gBAAsB;AACtB,yBAA+B;AAC/B,oBAAyC;AAEzC,0BAAmD;AAEnD,qBAA6B;AAC7B,0BAAmD;AACnD,+BAA+D;AAC/D,IAAAC,sBAA6B;AAC7B,kBAA+E;AAExE,SAAS,eAAuC,OAAe;AACrE,QAAM,UAAU,OAAO,OAAO,MAAM,oBAAM,OAAO,OAAO,CAAC;AACzD,QAAM,UAAmB,CAAC;AAC1B,QAAM,SAAkB,CAAC;AACzB,QAAM,cAA4B,CAAC;AACnC,QAAM,cAA4B,OAAO,OAAO,MAAM,qBAAQ,OAAO,iBAAiB,CAAC;AACvF,QAAM,oBAAwC,CAAC;AAC/C,QAAM,OAAO,MAAM,oBAAM,OAAO,IAAI;AACpC,QAAM,SAAS,MAAM,oBAAM,OAAO,MAAM;AAExC,QAAM,qBAAqB,MAAM,qBAAQ,OAAO,kBAAkB;AAElE,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,oBAAM,OAAO,kBAAkB,CAAC;AAC7E,eAAW,WAAW,OAAO,OAAO,WAAW,GAAG;AACjD,cAAI,kBAAG,SAAS,2BAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAClC,eAAW,kBAAG,SAAS,0BAAY,GAAG;AACrC,eAAO,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACjC,eAAW,kBAAG,SAAS,gDAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC5C,eAAW,kBAAG,SAAS,qCAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC,eAAW,kBAAG,SAAS,qCAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,cAGd,MAAgC;AACjC,SAAO;AAAA,IACN,GAAG,KAAK,iCAAc;AAAA,IACtB,GAAG,KAAK,gCAAY;AAAA,EACrB;AACD;AAEO,SAAS,0BAGd,MAA4C;AAC7C,SAAO;AAAA,IACN,GAAG,KAAK,iCAAc;AAAA,IACtB,GAAG,KAAK,oCAAwB;AAAA,EACjC;AACD;", "names": ["import_table", "import_view_common"]}