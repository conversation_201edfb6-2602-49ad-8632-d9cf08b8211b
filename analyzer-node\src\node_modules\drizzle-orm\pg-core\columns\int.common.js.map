{"version": 3, "sources": ["../../../src/pg-core/columns/int.common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnDataType,\n\tGeneratedIdentityConfig,\n\tIsIdentityByDefault,\n} from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { PgSequenceOptions } from '../sequence.ts';\nimport { PgColumnBuilder } from './common.ts';\n\nexport abstract class PgIntColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumnBuilder<\n\tT,\n\t{ generatedIdentity: GeneratedIdentityConfig }\n> {\n\tstatic readonly [entityKind]: string = 'PgIntColumnBaseBuilder';\n\n\tgeneratedAlwaysAsIdentity(\n\t\tsequence?: PgSequenceOptions & { name?: string },\n\t): IsIdentityByDefault<this, 'always'> {\n\t\tif (sequence) {\n\t\t\tconst { name, ...options } = sequence;\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'always',\n\t\t\t\tsequenceName: name,\n\t\t\t\tsequenceOptions: options,\n\t\t\t};\n\t\t} else {\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'always',\n\t\t\t};\n\t\t}\n\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\n\t\treturn this as any;\n\t}\n\n\tgeneratedByDefaultAsIdentity(\n\t\tsequence?: PgSequenceOptions & { name?: string },\n\t): IsIdentityByDefault<this, 'byDefault'> {\n\t\tif (sequence) {\n\t\t\tconst { name, ...options } = sequence;\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'byDefault',\n\t\t\t\tsequenceName: name,\n\t\t\t\tsequenceOptions: options,\n\t\t\t};\n\t\t} else {\n\t\t\tthis.config.generatedIdentity = {\n\t\t\t\ttype: 'byDefault',\n\t\t\t};\n\t\t}\n\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAMA,SAAS,kBAAkB;AAE3B,SAAS,uBAAuB;AAEzB,MAAe,+BAEZ,gBAGR;AAAA,EACD,QAAiB,UAAU,IAAY;AAAA,EAEvC,0BACC,UACsC;AACtC,QAAI,UAAU;AACb,YAAM,EAAE,MAAM,GAAG,QAAQ,IAAI;AAC7B,WAAK,OAAO,oBAAoB;AAAA,QAC/B,MAAM;AAAA,QACN,cAAc;AAAA,QACd,iBAAiB;AAAA,MAClB;AAAA,IACD,OAAO;AACN,WAAK,OAAO,oBAAoB;AAAA,QAC/B,MAAM;AAAA,MACP;AAAA,IACD;AAEA,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAEtB,WAAO;AAAA,EACR;AAAA,EAEA,6BACC,UACyC;AACzC,QAAI,UAAU;AACb,YAAM,EAAE,MAAM,GAAG,QAAQ,IAAI;AAC7B,WAAK,OAAO,oBAAoB;AAAA,QAC/B,MAAM;AAAA,QACN,cAAc;AAAA,QACd,iBAAiB;AAAA,MAClB;AAAA,IACD,OAAO;AACN,WAAK,OAAO,oBAAoB;AAAA,QAC/B,MAAM;AAAA,MACP;AAAA,IACD;AAEA,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAEtB,WAAO;AAAA,EACR;AACD;", "names": []}