{"version": 3, "sources": ["../src/tracing.ts"], "sourcesContent": ["import type { Span, Tracer } from '@opentelemetry/api';\nimport { iife } from '~/tracing-utils.ts';\nimport { npmVersion } from '~/version.ts';\n\nlet otel: typeof import('@opentelemetry/api') | undefined;\nlet rawTracer: Tracer | undefined;\n// try {\n// \totel = await import('@opentelemetry/api');\n// } catch (err: any) {\n// \tif (err.code !== 'MODULE_NOT_FOUND' && err.code !== 'ERR_MODULE_NOT_FOUND') {\n// \t\tthrow err;\n// \t}\n// }\n\ntype SpanName =\n\t| 'drizzle.operation'\n\t| 'drizzle.prepareQuery'\n\t| 'drizzle.buildSQL'\n\t| 'drizzle.execute'\n\t| 'drizzle.driver.execute'\n\t| 'drizzle.mapResponse';\n\n/** @internal */\nexport const tracer = {\n\tstartActiveSpan<F extends (span?: Span) => unknown>(name: SpanName, fn: F): ReturnType<F> {\n\t\tif (!otel) {\n\t\t\treturn fn() as ReturnType<F>;\n\t\t}\n\n\t\tif (!rawTracer) {\n\t\t\trawTracer = otel.trace.getTracer('drizzle-orm', npmVersion);\n\t\t}\n\n\t\treturn iife(\n\t\t\t(otel, rawTracer) =>\n\t\t\t\trawTracer.startActiveSpan(\n\t\t\t\t\tname,\n\t\t\t\t\t((span: Span) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\treturn fn(span);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tspan.setStatus({\n\t\t\t\t\t\t\t\tcode: otel.SpanStatusCode.ERROR,\n\t\t\t\t\t\t\t\tmessage: e instanceof Error ? e.message : 'Unknown error', // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthrow e;\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tspan.end();\n\t\t\t\t\t\t}\n\t\t\t\t\t}) as F,\n\t\t\t\t),\n\t\t\totel,\n\t\t\trawTracer,\n\t\t);\n\t},\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,2BAAqB;AACrB,qBAA2B;AAE3B,IAAI;AACJ,IAAI;AAkBG,MAAM,SAAS;AAAA,EACrB,gBAAoD,MAAgB,IAAsB;AACzF,QAAI,CAAC,MAAM;AACV,aAAO,GAAG;AAAA,IACX;AAEA,QAAI,CAAC,WAAW;AACf,kBAAY,KAAK,MAAM,UAAU,eAAe,yBAAU;AAAA,IAC3D;AAEA,eAAO;AAAA,MACN,CAACA,OAAMC,eACNA,WAAU;AAAA,QACT;AAAA,QACC,CAAC,SAAe;AAChB,cAAI;AACH,mBAAO,GAAG,IAAI;AAAA,UACf,SAAS,GAAG;AACX,iBAAK,UAAU;AAAA,cACd,MAAMD,MAAK,eAAe;AAAA,cAC1B,SAAS,aAAa,QAAQ,EAAE,UAAU;AAAA;AAAA,YAC3C,CAAC;AACD,kBAAM;AAAA,UACP,UAAE;AACD,iBAAK,IAAI;AAAA,UACV;AAAA,QACD;AAAA,MACD;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD;", "names": ["otel", "rawTracer"]}