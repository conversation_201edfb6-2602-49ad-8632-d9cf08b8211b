{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/select.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { SQL, View } from '~/sql/sql.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport type { SQLiteColumn } from '~/sqlite-core/columns/index.ts';\nimport type { SQLiteDialect } from '~/sqlite-core/dialect.ts';\nimport type { SQLiteSession } from '~/sqlite-core/session.ts';\nimport type { SubqueryWithSelection } from '~/sqlite-core/subquery.ts';\nimport type { SQLiteTable } from '~/sqlite-core/table.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport {\n\tapplyMixins,\n\tgetTableColumns,\n\tgetTableLikeName,\n\thaveSameKeys,\n\torderSelectedFields,\n\ttype ValueOrArray,\n} from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { SQLiteViewBase } from '../view-base.ts';\nimport type {\n\tAnySQLiteSelect,\n\tCreateSQLiteSelectFromBuilderMode,\n\tGetSQLiteSetOperators,\n\tSelectedFields,\n\tSetOperatorRightSelect,\n\tSQLiteCreateSetOperatorFn,\n\tSQLiteJoinFn,\n\tSQLiteSelectConfig,\n\tSQLiteSelectDynamic,\n\tSQLiteSelectExecute,\n\tSQLiteSelectHKT,\n\tSQLiteSelectHKTBase,\n\tSQLiteSelectPrepare,\n\tSQLiteSelectWithout,\n\tSQLiteSetOperatorExcludedMethods,\n\tSQLiteSetOperatorWithResult,\n} from './select.types.ts';\n\nexport class SQLiteSelectBuilder<\n\tTSelection extends SelectedFields | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTBuilderMode extends 'db' | 'qb' = 'db',\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteSelectBuilder';\n\n\tprivate fields: TSelection;\n\tprivate session: SQLiteSession<any, any, any, any> | undefined;\n\tprivate dialect: SQLiteDialect;\n\tprivate withList: Subquery[] | undefined;\n\tprivate distinct: boolean | undefined;\n\n\tconstructor(\n\t\tconfig: {\n\t\t\tfields: TSelection;\n\t\t\tsession: SQLiteSession<any, any, any, any> | undefined;\n\t\t\tdialect: SQLiteDialect;\n\t\t\twithList?: Subquery[];\n\t\t\tdistinct?: boolean;\n\t\t},\n\t) {\n\t\tthis.fields = config.fields;\n\t\tthis.session = config.session;\n\t\tthis.dialect = config.dialect;\n\t\tthis.withList = config.withList;\n\t\tthis.distinct = config.distinct;\n\t}\n\n\tfrom<TFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL>(\n\t\tsource: TFrom,\n\t): CreateSQLiteSelectFromBuilderMode<\n\t\tTBuilderMode,\n\t\tGetSelectTableName<TFrom>,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection extends undefined ? GetSelectTableSelection<TFrom> : TSelection,\n\t\tTSelection extends undefined ? 'single' : 'partial'\n\t> {\n\t\tconst isPartialSelect = !!this.fields;\n\n\t\tlet fields: SelectedFields;\n\t\tif (this.fields) {\n\t\t\tfields = this.fields;\n\t\t} else if (is(source, Subquery)) {\n\t\t\t// This is required to use the proxy handler to get the correct field values from the subquery\n\t\t\tfields = Object.fromEntries(\n\t\t\t\tObject.keys(source._.selectedFields).map((\n\t\t\t\t\tkey,\n\t\t\t\t) => [key, source[key as unknown as keyof typeof source] as unknown as SelectedFields[string]]),\n\t\t\t);\n\t\t} else if (is(source, SQLiteViewBase)) {\n\t\t\tfields = source[ViewBaseConfig].selectedFields as SelectedFields;\n\t\t} else if (is(source, SQL)) {\n\t\t\tfields = {};\n\t\t} else {\n\t\t\tfields = getTableColumns<SQLiteTable>(source);\n\t\t}\n\n\t\treturn new SQLiteSelectBase({\n\t\t\ttable: source,\n\t\t\tfields,\n\t\t\tisPartialSelect,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\twithList: this.withList,\n\t\t\tdistinct: this.distinct,\n\t\t}) as any;\n\t}\n}\n\nexport abstract class SQLiteSelectQueryBuilderBase<\n\tTHKT extends SQLiteSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends TypedQueryBuilder<TSelectedFields, TResult> {\n\tstatic readonly [entityKind]: string = 'SQLiteSelectQueryBuilder';\n\n\toverride readonly _: {\n\t\treadonly dialect: 'sqlite';\n\t\treadonly hkt: THKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly resultType: TResultType;\n\t\treadonly runResult: TRunResult;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n\n\t/** @internal */\n\tconfig: SQLiteSelectConfig;\n\tprotected joinsNotNullableMap: Record<string, boolean>;\n\tprivate tableName: string | undefined;\n\tprivate isPartialSelect: boolean;\n\tprotected session: SQLiteSession<any, any, any, any> | undefined;\n\tprotected dialect: SQLiteDialect;\n\n\tconstructor(\n\t\t{ table, fields, isPartialSelect, session, dialect, withList, distinct }: {\n\t\t\ttable: SQLiteSelectConfig['table'];\n\t\t\tfields: SQLiteSelectConfig['fields'];\n\t\t\tisPartialSelect: boolean;\n\t\t\tsession: SQLiteSession<any, any, any, any> | undefined;\n\t\t\tdialect: SQLiteDialect;\n\t\t\twithList: Subquery[] | undefined;\n\t\t\tdistinct: boolean | undefined;\n\t\t},\n\t) {\n\t\tsuper();\n\t\tthis.config = {\n\t\t\twithList,\n\t\t\ttable,\n\t\t\tfields: { ...fields },\n\t\t\tdistinct,\n\t\t\tsetOperators: [],\n\t\t};\n\t\tthis.isPartialSelect = isPartialSelect;\n\t\tthis.session = session;\n\t\tthis.dialect = dialect;\n\t\tthis._ = {\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t} as this['_'];\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): SQLiteJoinFn<this, TDynamic, TJoinType> {\n\t\treturn (\n\t\t\ttable: SQLiteTable | Subquery | SQLiteViewBase | SQL,\n\t\t\ton: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst baseTableName = this.tableName;\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins?.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (!this.isPartialSelect) {\n\t\t\t\t// If this is the first join and this is not a partial select and we're not selecting from raw SQL, \"move\" the fields from the main table to the nested object\n\t\t\t\tif (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === 'string') {\n\t\t\t\t\tthis.config.fields = {\n\t\t\t\t\t\t[baseTableName]: this.config.fields,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (typeof tableName === 'string' && !is(table, SQL)) {\n\t\t\t\t\tconst selection = is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, View)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: table[Table.Symbol.Columns];\n\t\t\t\t\tthis.config.fields[tableName] = selection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.fields,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as TSelection,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (!this.config.joins) {\n\t\t\t\tthis.config.joins = [];\n\t\t\t}\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Executes a `left join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tleftJoin = this.createJoin('left');\n\n\t/**\n\t * Executes a `right join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\trightJoin = this.createJoin('right');\n\n\t/**\n\t * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tinnerJoin = this.createJoin('inner');\n\n\t/**\n\t * Executes a `full join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tfullJoin = this.createJoin('full');\n\n\tprivate createSetOperator(\n\t\ttype: SetOperator,\n\t\tisAll: boolean,\n\t): <TValue extends SQLiteSetOperatorWithResult<TResult>>(\n\t\trightSelection:\n\t\t\t| ((setOperators: GetSQLiteSetOperators) => SetOperatorRightSelect<TValue, TResult>)\n\t\t\t| SetOperatorRightSelect<TValue, TResult>,\n\t) => SQLiteSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tSQLiteSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\treturn (rightSelection) => {\n\t\t\tconst rightSelect = (typeof rightSelection === 'function'\n\t\t\t\t? rightSelection(getSQLiteSetOperators())\n\t\t\t\t: rightSelection) as TypedQueryBuilder<\n\t\t\t\t\tany,\n\t\t\t\t\tTResult\n\t\t\t\t>;\n\n\t\t\tif (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.setOperators.push({ type, isAll, rightSelect });\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Adds `union` set operator to the query.\n\t *\n\t * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique names from customers and users tables\n\t * await db.select({ name: users.name })\n\t *   .from(users)\n\t *   .union(\n\t *     db.select({ name: customers.name }).from(customers)\n\t *   );\n\t * // or\n\t * import { union } from 'drizzle-orm/sqlite-core'\n\t *\n\t * await union(\n\t *   db.select({ name: users.name }).from(users),\n\t *   db.select({ name: customers.name }).from(customers)\n\t * );\n\t * ```\n\t */\n\tunion = this.createSetOperator('union', false);\n\n\t/**\n\t * Adds `union all` set operator to the query.\n\t *\n\t * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all transaction ids from both online and in-store sales\n\t * await db.select({ transaction: onlineSales.transactionId })\n\t *   .from(onlineSales)\n\t *   .unionAll(\n\t *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t *   );\n\t * // or\n\t * import { unionAll } from 'drizzle-orm/sqlite-core'\n\t *\n\t * await unionAll(\n\t *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n\t *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t * );\n\t * ```\n\t */\n\tunionAll = this.createSetOperator('union', true);\n\n\t/**\n\t * Adds `intersect` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select course names that are offered in both departments A and B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .intersect(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { intersect } from 'drizzle-orm/sqlite-core'\n\t *\n\t * await intersect(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\tintersect = this.createSetOperator('intersect', false);\n\n\t/**\n\t * Adds `except` set operator to the query.\n\t *\n\t * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all courses offered in department A but not in department B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .except(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { except } from 'drizzle-orm/sqlite-core'\n\t *\n\t * await except(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\texcept = this.createSetOperator('except', false);\n\n\t/** @internal */\n\taddSetOperators(setOperators: SQLiteSelectConfig['setOperators']): SQLiteSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tSQLiteSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\tthis.config.setOperators.push(...setOperators);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be selected.\n\t *\n\t * ```ts\n\t * // Select all cars with green color\n\t * await db.select().from(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Select all BMW cars with a green color\n\t * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Select all cars with the green or blue color\n\t * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(\n\t\twhere: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t): SQLiteSelectWithout<this, TDynamic, 'where'> {\n\t\tif (typeof where === 'function') {\n\t\t\twhere = where(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `having` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @param having the `having` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all brands with more than one car\n\t * await db.select({\n\t * \tbrand: cars.brand,\n\t * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n\t * })\n\t *   .from(cars)\n\t *   .groupBy(cars.brand)\n\t *   .having(({ count }) => gt(count, 1));\n\t * ```\n\t */\n\thaving(\n\t\thaving: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): SQLiteSelectWithout<this, TDynamic, 'having'> {\n\t\tif (typeof having === 'function') {\n\t\t\thaving = having(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.having = having;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `group by` clause to the query.\n\t *\n\t * Calling this method will group rows that have the same values into summary rows, often used for aggregation purposes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Group and count people by their last names\n\t * await db.select({\n\t *    lastName: people.lastName,\n\t *    count: sql<number>`cast(count(*) as int)`\n\t * })\n\t *   .from(people)\n\t *   .groupBy(people.lastName);\n\t * ```\n\t */\n\tgroupBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>,\n\t): SQLiteSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(...columns: (SQLiteColumn | SQL)[]): SQLiteSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>]\n\t\t\t| (SQLiteColumn | SQL | SQL.Aliased)[]\n\t): SQLiteSelectWithout<this, TDynamic, 'groupBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst groupBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t\tthis.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n\t\t} else {\n\t\t\tthis.config.groupBy = columns as (SQLiteColumn | SQL | SQL.Aliased)[];\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `order by` clause to the query.\n\t *\n\t * Calling this method will sort the result-set in ascending or descending order. By default, the sort order is ascending.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#order-by}\n\t *\n\t * @example\n\t *\n\t * ```\n\t * // Select cars ordered by year\n\t * await db.select().from(cars).orderBy(cars.year);\n\t * ```\n\t *\n\t * You can specify whether results are in ascending or descending order with the `asc()` and `desc()` operators.\n\t *\n\t * ```ts\n\t * // Select cars ordered by year in descending order\n\t * await db.select().from(cars).orderBy(desc(cars.year));\n\t *\n\t * // Select cars ordered by year and price\n\t * await db.select().from(cars).orderBy(asc(cars.year), desc(cars.price));\n\t * ```\n\t */\n\torderBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>,\n\t): SQLiteSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (SQLiteColumn | SQL)[]): SQLiteSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>]\n\t\t\t| (SQLiteColumn | SQL | SQL.Aliased)[]\n\t): SQLiteSelectWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t} else {\n\t\t\tconst orderByArray = columns as (SQLiteColumn | SQL | SQL.Aliased)[];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `limit` clause to the query.\n\t *\n\t * Calling this method will set the maximum number of rows that will be returned by this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param limit the `limit` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the first 10 people from this query.\n\t * await db.select().from(people).limit(10);\n\t * ```\n\t */\n\tlimit(limit: number | Placeholder): SQLiteSelectWithout<this, TDynamic, 'limit'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.limit = limit;\n\t\t} else {\n\t\t\tthis.config.limit = limit;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `offset` clause to the query.\n\t *\n\t * Calling this method will skip a number of rows when returning results from this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param offset the `offset` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the 10th-20th people from this query.\n\t * await db.select().from(people).offset(10).limit(10);\n\t * ```\n\t */\n\toffset(offset: number | Placeholder): SQLiteSelectWithout<this, TDynamic, 'offset'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.offset = offset;\n\t\t} else {\n\t\t\tthis.config.offset = offset;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildSelectQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tas<TAlias extends string>(\n\t\talias: TAlias,\n\t): SubqueryWithSelection<this['_']['selectedFields'], TAlias> {\n\t\treturn new Proxy(\n\t\t\tnew Subquery(this.getSQL(), this.config.fields, alias),\n\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as SubqueryWithSelection<this['_']['selectedFields'], TAlias>;\n\t}\n\n\t/** @internal */\n\toverride getSelectedFields(): this['_']['selectedFields'] {\n\t\treturn new Proxy(\n\t\t\tthis.config.fields,\n\t\t\tnew SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): SQLiteSelectDynamic<this> {\n\t\treturn this;\n\t}\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface SQLiteSelectBase<\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode = 'single',\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends\n\tSQLiteSelectQueryBuilderBase<\n\t\tSQLiteSelectHKT,\n\t\tTTableName,\n\t\tTResultType,\n\t\tTRunResult,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tQueryPromise<TResult>\n{}\n\nexport class SQLiteSelectBase<\n\tTTableName extends string | undefined,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTSelection,\n\tTSelectMode extends SelectMode = 'single',\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends SQLiteSelectQueryBuilderBase<\n\tSQLiteSelectHKT,\n\tTTableName,\n\tTResultType,\n\tTRunResult,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\tTDynamic,\n\tTExcludedMethods,\n\tTResult,\n\tTSelectedFields\n> implements RunnableQuery<TResult, 'sqlite'>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'SQLiteSelect';\n\n\t/** @internal */\n\t_prepare(isOneTimeQuery = true): SQLiteSelectPrepare<this> {\n\t\tif (!this.session) {\n\t\t\tthrow new Error('Cannot execute a query on a query builder. Please use a database instance instead.');\n\t\t}\n\t\tconst fieldsList = orderSelectedFields<SQLiteColumn>(this.config.fields);\n\t\tconst query = this.session[isOneTimeQuery ? 'prepareOneTimeQuery' : 'prepareQuery'](\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tfieldsList,\n\t\t\t'all',\n\t\t\ttrue,\n\t\t);\n\t\tquery.joinsNotNullableMap = this.joinsNotNullableMap;\n\t\treturn query as ReturnType<this['prepare']>;\n\t}\n\n\tprepare(): SQLiteSelectPrepare<this> {\n\t\treturn this._prepare(false);\n\t}\n\n\trun: ReturnType<this['prepare']>['run'] = (placeholderValues) => {\n\t\treturn this._prepare().run(placeholderValues);\n\t};\n\n\tall: ReturnType<this['prepare']>['all'] = (placeholderValues) => {\n\t\treturn this._prepare().all(placeholderValues);\n\t};\n\n\tget: ReturnType<this['prepare']>['get'] = (placeholderValues) => {\n\t\treturn this._prepare().get(placeholderValues);\n\t};\n\n\tvalues: ReturnType<this['prepare']>['values'] = (placeholderValues) => {\n\t\treturn this._prepare().values(placeholderValues);\n\t};\n\n\tasync execute(): Promise<SQLiteSelectExecute<this>> {\n\t\treturn this.all() as SQLiteSelectExecute<this>;\n\t}\n}\n\napplyMixins(SQLiteSelectBase, [QueryPromise]);\n\nfunction createSetOperator(type: SetOperator, isAll: boolean): SQLiteCreateSetOperatorFn {\n\treturn (leftSelect, rightSelect, ...restSelects) => {\n\t\tconst setOperators = [rightSelect, ...restSelects].map((select) => ({\n\t\t\ttype,\n\t\t\tisAll,\n\t\t\trightSelect: select as AnySQLiteSelect,\n\t\t}));\n\n\t\tfor (const setOperator of setOperators) {\n\t\t\tif (!haveSameKeys((leftSelect as any).getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\treturn (leftSelect as AnySQLiteSelect).addSetOperators(setOperators) as any;\n\t};\n}\n\nconst getSQLiteSetOperators = () => ({\n\tunion,\n\tunionAll,\n\tintersect,\n\texcept,\n});\n\n/**\n * Adds `union` set operator to the query.\n *\n * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n *\n * @example\n *\n * ```ts\n * // Select all unique names from customers and users tables\n * import { union } from 'drizzle-orm/sqlite-core'\n *\n * await union(\n *   db.select({ name: users.name }).from(users),\n *   db.select({ name: customers.name }).from(customers)\n * );\n * // or\n * await db.select({ name: users.name })\n *   .from(users)\n *   .union(\n *     db.select({ name: customers.name }).from(customers)\n *   );\n * ```\n */\nexport const union = createSetOperator('union', false);\n\n/**\n * Adds `union all` set operator to the query.\n *\n * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n *\n * @example\n *\n * ```ts\n * // Select all transaction ids from both online and in-store sales\n * import { unionAll } from 'drizzle-orm/sqlite-core'\n *\n * await unionAll(\n *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n * );\n * // or\n * await db.select({ transaction: onlineSales.transactionId })\n *   .from(onlineSales)\n *   .unionAll(\n *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n *   );\n * ```\n */\nexport const unionAll = createSetOperator('union', true);\n\n/**\n * Adds `intersect` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n *\n * @example\n *\n * ```ts\n * // Select course names that are offered in both departments A and B\n * import { intersect } from 'drizzle-orm/sqlite-core'\n *\n * await intersect(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .intersect(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const intersect = createSetOperator('intersect', false);\n\n/**\n * Adds `except` set operator to the query.\n *\n * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n *\n * @example\n *\n * ```ts\n * // Select all courses offered in department A but not in department B\n * import { except } from 'drizzle-orm/sqlite-core'\n *\n * await except(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .except(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const except = createSetOperator('except', false);\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAC/B,2BAAkC;AAWlC,2BAA6B;AAE7B,6BAAsC;AACtC,iBAA0B;AAO1B,sBAAyB;AACzB,mBAAsB;AACtB,mBAOO;AACP,yBAA+B;AAC/B,uBAA+B;AAoBxB,MAAM,oBAKX;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAER,YACC,QAOC;AACD,SAAK,SAAS,OAAO;AACrB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,SAAK,WAAW,OAAO;AACvB,SAAK,WAAW,OAAO;AAAA,EACxB;AAAA,EAEA,KACC,QAQC;AACD,UAAM,kBAAkB,CAAC,CAAC,KAAK;AAE/B,QAAI;AACJ,QAAI,KAAK,QAAQ;AAChB,eAAS,KAAK;AAAA,IACf,eAAW,kBAAG,QAAQ,wBAAQ,GAAG;AAEhC,eAAS,OAAO;AAAA,QACf,OAAO,KAAK,OAAO,EAAE,cAAc,EAAE,IAAI,CACxC,QACI,CAAC,KAAK,OAAO,GAAqC,CAAsC,CAAC;AAAA,MAC/F;AAAA,IACD,eAAW,kBAAG,QAAQ,+BAAc,GAAG;AACtC,eAAS,OAAO,iCAAc,EAAE;AAAA,IACjC,eAAW,kBAAG,QAAQ,cAAG,GAAG;AAC3B,eAAS,CAAC;AAAA,IACX,OAAO;AACN,mBAAS,8BAA6B,MAAM;AAAA,IAC7C;AAEA,WAAO,IAAI,iBAAiB;AAAA,MAC3B,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IAChB,CAAC;AAAA,EACF;AACD;AAEO,MAAe,qCAaZ,uCAA4C;AAAA,EACrD,QAAiB,wBAAU,IAAY;AAAA,EAErB;AAAA;AAAA,EAgBlB;AAAA,EACU;AAAA,EACF;AAAA,EACA;AAAA,EACE;AAAA,EACA;AAAA,EAEV,YACC,EAAE,OAAO,QAAQ,iBAAiB,SAAS,SAAS,UAAU,SAAS,GAStE;AACD,UAAM;AACN,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,QAAQ,EAAE,GAAG,OAAO;AAAA,MACpB;AAAA,MACA,cAAc,CAAC;AAAA,IAChB;AACA,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,IAAI;AAAA,MACR,gBAAgB;AAAA,IACjB;AACA,SAAK,gBAAY,+BAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AAAA,EAC/F;AAAA,EAEQ,WACP,UAC0C;AAC1C,WAAO,CACN,OACA,OACI;AACJ,YAAM,gBAAgB,KAAK;AAC3B,YAAM,gBAAY,+BAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AACjG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;AAAA,MACrE;AAEA,UAAI,CAAC,KAAK,iBAAiB;AAE1B,YAAI,OAAO,KAAK,KAAK,mBAAmB,EAAE,WAAW,KAAK,OAAO,kBAAkB,UAAU;AAC5F,eAAK,OAAO,SAAS;AAAA,YACpB,CAAC,aAAa,GAAG,KAAK,OAAO;AAAA,UAC9B;AAAA,QACD;AACA,YAAI,OAAO,cAAc,YAAY,KAAC,kBAAG,OAAO,cAAG,GAAG;AACrD,gBAAM,gBAAY,kBAAG,OAAO,wBAAQ,IACjC,MAAM,EAAE,qBACR,kBAAG,OAAO,eAAI,IACd,MAAM,iCAAc,EAAE,iBACtB,MAAM,mBAAM,OAAO,OAAO;AAC7B,eAAK,OAAO,OAAO,SAAS,IAAI;AAAA,QACjC;AAAA,MACD;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK;AAAA,UACJ,IAAI;AAAA,YACH,KAAK,OAAO;AAAA,YACZ,IAAI,6CAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,OAAO,OAAO;AACvB,aAAK,OAAO,QAAQ,CAAC;AAAA,MACtB;AACA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;AAAA,UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,WAAW,KAAK,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BjC,YAAY,KAAK,WAAW,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BnC,YAAY,KAAK,WAAW,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BnC,WAAW,KAAK,WAAW,MAAM;AAAA,EAEzB,kBACP,MACA,OAUC;AACD,WAAO,CAAC,mBAAmB;AAC1B,YAAM,cAAe,OAAO,mBAAmB,aAC5C,eAAe,sBAAsB,CAAC,IACtC;AAKH,UAAI,KAAC,2BAAa,KAAK,kBAAkB,GAAG,YAAY,kBAAkB,CAAC,GAAG;AAC7E,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,WAAK,OAAO,aAAa,KAAK,EAAE,MAAM,OAAO,YAAY,CAAC;AAC1D,aAAO;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,QAAQ,KAAK,kBAAkB,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2B7C,WAAW,KAAK,kBAAkB,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2B/C,YAAY,KAAK,kBAAkB,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BrD,SAAS,KAAK,kBAAkB,UAAU,KAAK;AAAA;AAAA,EAG/C,gBAAgB,cAKd;AACD,SAAK,OAAO,aAAa,KAAK,GAAG,YAAY;AAC7C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,MACC,OAC+C;AAC/C,QAAI,OAAO,UAAU,YAAY;AAChC,cAAQ;AAAA,QACP,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,6CAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AACA,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,OACC,QACgD;AAChD,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS;AAAA,QACR,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,6CAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AACA,SAAK,OAAO,SAAS;AACrB,WAAO;AAAA,EACR;AAAA,EAyBA,WACI,SAG8C;AACjD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAAA,IAClE,OAAO;AACN,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EA8BA,WACI,SAG8C;AACjD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAEhE,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;AAAA,MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;AAAA,MACvB;AAAA,IACD,OAAO;AACN,YAAM,eAAe;AAErB,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;AAAA,MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;AAAA,MACvB;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,OAA2E;AAChF,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,QAAQ;AAAA,IAC1C,OAAO;AACN,WAAK,OAAO,QAAQ;AAAA,IACrB;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,QAA6E;AACnF,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,SAAS;AAAA,IAC3C,OAAO;AACN,WAAK,OAAO,SAAS;AAAA,IACtB;AACA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,GACC,OAC6D;AAC7D,WAAO,IAAI;AAAA,MACV,IAAI,yBAAS,KAAK,OAAO,GAAG,KAAK,OAAO,QAAQ,KAAK;AAAA,MACrD,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,IACvF;AAAA,EACD;AAAA;AAAA,EAGS,oBAAiD;AACzD,WAAO,IAAI;AAAA,MACV,KAAK,OAAO;AAAA,MACZ,IAAI,6CAAsB,EAAE,OAAO,KAAK,WAAW,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,IACvG;AAAA,EACD;AAAA,EAEA,WAAsC;AACrC,WAAO;AAAA,EACR;AACD;AAgCO,MAAM,yBAYH,6BAYgD;AAAA,EACzD,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC,SAAS,iBAAiB,MAAiC;AAC1D,QAAI,CAAC,KAAK,SAAS;AAClB,YAAM,IAAI,MAAM,oFAAoF;AAAA,IACrG;AACA,UAAM,iBAAa,kCAAkC,KAAK,OAAO,MAAM;AACvE,UAAM,QAAQ,KAAK,QAAQ,iBAAiB,wBAAwB,cAAc;AAAA,MACjF,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AACA,UAAM,sBAAsB,KAAK;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,UAAqC;AACpC,WAAO,KAAK,SAAS,KAAK;AAAA,EAC3B;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,SAAgD,CAAC,sBAAsB;AACtE,WAAO,KAAK,SAAS,EAAE,OAAO,iBAAiB;AAAA,EAChD;AAAA,EAEA,MAAM,UAA8C;AACnD,WAAO,KAAK,IAAI;AAAA,EACjB;AACD;AAAA,IAEA,0BAAY,kBAAkB,CAAC,iCAAY,CAAC;AAE5C,SAAS,kBAAkB,MAAmB,OAA2C;AACxF,SAAO,CAAC,YAAY,gBAAgB,gBAAgB;AACnD,UAAM,eAAe,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAAC,YAAY;AAAA,MACnE;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACd,EAAE;AAEF,eAAW,eAAe,cAAc;AACvC,UAAI,KAAC,2BAAc,WAAmB,kBAAkB,GAAG,YAAY,YAAY,kBAAkB,CAAC,GAAG;AACxG,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAQ,WAA+B,gBAAgB,YAAY;AAAA,EACpE;AACD;AAEA,MAAM,wBAAwB,OAAO;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AA2BO,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AA2B9C,MAAM,WAAW,kBAAkB,SAAS,IAAI;AA2BhD,MAAM,YAAY,kBAAkB,aAAa,KAAK;AA2BtD,MAAM,SAAS,kBAAkB,UAAU,KAAK;", "names": []}