{"version": 3, "sources": ["../../../src/sql/functions/vector.ts"], "sourcesContent": ["import type { AnyColumn } from '~/index.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { type SQL, sql, type SQLWrapper } from '../sql.ts';\n\nfunction toSql(value: number[] | string[]): string {\n\treturn JSON.stringify(value);\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the L2 distance to the given value.\n * If used in querying, this specifies that it should return the L2 distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l2Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l2Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l2Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <-> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <-> ${value}`;\n}\n\n/**\n * L1 distance is one of the possible distance measures between two probability distribution vectors and it is\n * calculated as the sum of the absolute differences.\n * The smaller the distance between the observed probability vectors, the higher the accuracy of the synthetic data\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(l1Distance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: l1Distance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function l1Distance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <+> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <+> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the inner product distance to the given value.\n * If used in querying, this specifies that it should return the inner product distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(innerProduct(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({ distance: innerProduct(cars.embedding, embedding) }).from(cars)\n * ```\n */\nexport function innerProduct(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <#> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <#> ${value}`;\n}\n\n/**\n * Used in sorting and in querying, if used in sorting,\n * this specifies that the given column or expression should be sorted in an order\n * that minimizes the cosine distance to the given value.\n * If used in querying, this specifies that it should return the cosine distance\n * between the given column or expression and the given value.\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(cosineDistance(cars.embedding, embedding));\n * ```\n *\n * ```ts\n * // Select distance of cars and embedding\n * // to the given embedding\n * db.select({distance: cosineDistance(cars.embedding, embedding)}).from(cars)\n * ```\n */\nexport function cosineDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <=> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <=> ${value}`;\n}\n\n/**\n * Hamming distance between two strings or vectors of equal length is the number of positions at which the\n * corresponding symbols are different. In other words, it measures the minimum number of\n * substitutions required to change one string into the other, or equivalently,\n * the minimum number of errors that could have transformed one string into the other\n *\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(hammingDistance(cars.embedding, embedding));\n * ```\n */\nexport function hammingDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <~> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <~> ${value}`;\n}\n\n/**\n * ## Examples\n *\n * ```ts\n * // Sort cars by embedding similarity\n * // to the given embedding\n * db.select().from(cars)\n *   .orderBy(jaccardDistance(cars.embedding, embedding));\n * ```\n */\nexport function jaccardDistance(\n\tcolumn: SQLWrapper | AnyColumn,\n\tvalue: number[] | string[] | TypedQueryBuilder<any> | string,\n): SQL {\n\tif (Array.isArray(value)) {\n\t\treturn sql`${column} <%> ${toSql(value)}`;\n\t}\n\treturn sql`${column} <%> ${value}`;\n}\n"], "mappings": "AAEA,SAAmB,WAA4B;AAE/C,SAAS,MAAM,OAAoC;AAClD,SAAO,KAAK,UAAU,KAAK;AAC5B;AAwBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAsBO,SAAS,WACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,aACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAwBO,SAAS,eACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAiBO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;AAYO,SAAS,gBACf,QACA,OACM;AACN,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO,MAAM,MAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,MAAM,QAAQ,KAAK;AACjC;", "names": []}