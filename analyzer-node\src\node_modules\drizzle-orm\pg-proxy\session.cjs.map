{"version": 3, "sources": ["../../src/pg-proxy/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery as PreparedQueryBase, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { QueryWithTypings } from '~/sql/sql.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\nimport type { RemoteCallback } from './driver.ts';\n\nexport interface PgRemoteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PgRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<PgRemoteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgRemoteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: PgRemoteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig>(\n\t\tquery: QueryWithTypings,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PreparedQuery<T> {\n\t\treturn new PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tquery.typings,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: PgProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t_config?: PgTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the Postgres Proxy driver');\n\t}\n}\n\nexport class PgProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<PgRemoteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PgProxyTransaction';\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: PgProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the Postgres Proxy driver');\n\t}\n}\n\nexport class PreparedQuery<T extends PreparedQueryConfig> extends PreparedQueryBase<T> {\n\tstatic readonly [entityKind]: string = 'PgProxyPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate typings: any[] | undefined,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tconst { fields, client, queryString, joinsNotNullableMap, customResultMapper, logger, typings } = this;\n\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\n\t\t\tlogger.logQuery(queryString, params);\n\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\t\tconst { rows } = await client(queryString, params as any[], 'execute', typings);\n\n\t\t\t\t\treturn rows;\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst rows = await tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\tconst { rows } = await client(queryString, params as any[], 'all', typings);\n\n\t\t\t\treturn rows;\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(rows)\n\t\t\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tasync all() {}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface PgRemoteQueryResultHKT extends PgQueryResultHKT {\n\ttype: Assume<this['row'], {\n\t\t[column: string]: any;\n\t}>[];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,oBAA2B;AAE3B,qBAA8B;AAG9B,qBAAgE;AAGhE,iBAAiC;AACjC,qBAAuB;AACvB,mBAA0C;AAOnC,MAAM,wBAGH,yBAAwD;AAAA,EAKjE,YACS,QACR,SACQ,QACR,UAAkC,CAAC,GAClC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACmB;AACnB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,YACd,cACA,SACa;AACb,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC9E;AACD;AAEO,MAAM,2BAGH,6BAA4D;AAAA,EACrE,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAe,YACd,cACa;AACb,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC9E;AACD;AAEO,MAAM,sBAAqD,eAAAA,gBAAqB;AAAA,EAGtF,YACS,QACA,aACA,QACA,SACA,QACA,QACA,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAT1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAbA,QAAiB,wBAAU,IAAY;AAAA,EAevC,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,sBAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,YAAM,EAAE,QAAQ,QAAQ,aAAa,qBAAqB,oBAAoB,QAAQ,QAAQ,IAAI;AAElG,YAAM,cAAc;AAAA,QACnB,sBAAsB;AAAA,QACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AAED,aAAO,SAAS,aAAa,MAAM;AAEnC,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,sBAAO,gBAAgB,0BAA0B,YAAY;AACnE,gBAAM,EAAE,MAAAC,MAAK,IAAI,MAAM,OAAO,aAAa,QAAiB,WAAW,OAAO;AAE9E,iBAAOA;AAAA,QACR,CAAC;AAAA,MACF;AAEA,YAAM,OAAO,MAAM,sBAAO,gBAAgB,0BAA0B,YAAY;AAC/E,cAAM,cAAc;AAAA,UACnB,sBAAsB;AAAA,UACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AAED,cAAM,EAAE,MAAAA,MAAK,IAAI,MAAM,OAAO,aAAa,QAAiB,OAAO,OAAO;AAE1E,eAAOA;AAAA,MACR,CAAC;AAED,aAAO,sBAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MACnF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,MAAM;AAAA,EAAC;AAAA;AAAA,EAGb,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": ["PreparedQueryBase", "rows"]}