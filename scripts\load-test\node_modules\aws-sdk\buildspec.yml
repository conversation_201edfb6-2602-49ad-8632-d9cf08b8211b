version: 0.2

env:
  variables:
    AWS_REGION: "us-west-2"
    CHROME_BIN: "/usr/bin/chromium-browser"

phases:
  install:
    runtime-versions:
      nodejs: 12
  pre_build:
    commands:
      - echo Install npm dependencies
      - npm install
      - gem install rake
  build:
    commands:
      - echo Running Test
      - npm run test
  post_build:
    commands:
      - ./node_modules/.bin/codecov -f coverage/*.json -t e89a5f73-fd56-45de-8799-009a19bde63c
