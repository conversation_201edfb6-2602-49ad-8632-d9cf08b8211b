{"version": 3, "sources": ["../../src/postgres-js/session.ts"], "sourcesContent": ["import type { Row, RowList, Sql, TransactionSql } from 'postgres';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport class PostgresJsPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'PostgresJsPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: Sql,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\t\tconst { fields, queryString: query, client, joinsNotNullableMap, customResultMapper } = this;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\t\treturn client.unsafe(query, params as any[]);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst rows = await tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': query,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\treturn client.unsafe(query, params as any[]).values();\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(rows)\n\t\t\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn this.client.unsafe(this.queryString, params as any[]);\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface PostgresJsSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PostgresJsSession<\n\tTSQL extends Sql,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<PostgresJsQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PostgresJsSession';\n\n\tlogger: Logger;\n\n\tconstructor(\n\t\tpublic client: TSQL,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\t/** @internal */\n\t\treadonly options: PostgresJsSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new PostgresJsPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tquery(query: string, params: unknown[]): Promise<RowList<Row[]>> {\n\t\tthis.logger.logQuery(query, params);\n\t\treturn this.client.unsafe(query, params as any[]).values();\n\t}\n\n\tqueryObjects<T extends Row>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<RowList<T[]>> {\n\t\treturn this.client.unsafe(query, params as any[]);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: PostgresJsTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.client.begin(async (client) => {\n\t\t\tconst session = new PostgresJsSession<TransactionSql, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.options,\n\t\t\t);\n\t\t\tconst tx = new PostgresJsTransaction(this.dialect, session, this.schema);\n\t\t\tif (config) {\n\t\t\t\tawait tx.setTransaction(config);\n\t\t\t}\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport class PostgresJsTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<PostgresJsQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'PostgresJsTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\t/** @internal */\n\t\toverride readonly session: PostgresJsSession<TransactionSql, TFullSchema, TSchema>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tnestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema, nestedIndex);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: PostgresJsTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn this.session.client.savepoint((client) => {\n\t\t\tconst session = new PostgresJsSession<TransactionSql, TFullSchema, TSchema>(\n\t\t\t\tclient,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.session.options,\n\t\t\t);\n\t\t\tconst tx = new PostgresJsTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\t\treturn transaction(tx);\n\t\t}) as Promise<T>;\n\t}\n}\n\nexport interface PostgresJsQueryResultHKT extends PgQueryResultHKT {\n\ttype: RowList<Assume<this['row'], Row>[]>;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAG9B,SAAS,iBAAiB,iBAAiB;AAE3C,SAAS,wBAAoC;AAC7C,SAAS,cAAc;AACvB,SAAsB,oBAAoB;AAEnC,MAAM,gCAA+D,gBAAmB;AAAA,EAG9F,YACS,QACA,aACA,QACA,QACA,QACA,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAR1B;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA,EAcvC,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,OAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,YAAM,cAAc;AAAA,QACnB,sBAAsB,KAAK;AAAA,QAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AAED,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,YAAM,EAAE,QAAQ,aAAa,OAAO,QAAQ,qBAAqB,mBAAmB,IAAI;AACxF,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,OAAO,gBAAgB,0BAA0B,MAAM;AAC7D,iBAAO,OAAO,OAAO,OAAO,MAAe;AAAA,QAC5C,CAAC;AAAA,MACF;AAEA,YAAM,OAAO,MAAM,OAAO,gBAAgB,0BAA0B,MAAM;AACzE,cAAM,cAAc;AAAA,UACnB,sBAAsB;AAAA,UACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AAED,eAAO,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;AAAA,MACrD,CAAC;AAED,aAAO,OAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MACnF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,WAAO,OAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,YAAM,cAAc;AAAA,QACnB,sBAAsB,KAAK;AAAA,QAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AACD,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,aAAO,OAAO,gBAAgB,0BAA0B,MAAM;AAC7D,cAAM,cAAc;AAAA,UACnB,sBAAsB,KAAK;AAAA,UAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AACD,eAAO,KAAK,OAAO,OAAO,KAAK,aAAa,MAAe;AAAA,MAC5D,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,0BAIH,UAA0D;AAAA,EAKnE,YACQ,QACP,SACQ,QAEC,UAAoC,CAAC,GAC7C;AACD,UAAM,OAAO;AANN;AAEC;AAEC;AAGT,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAbA,QAAiB,UAAU,IAAY;AAAA,EAEvC;AAAA,EAaA,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,OAAe,QAA4C;AAChE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe,EAAE,OAAO;AAAA,EAC1D;AAAA,EAEA,aACC,OACA,QACwB;AACxB,WAAO,KAAK,OAAO,OAAO,OAAO,MAAe;AAAA,EACjD;AAAA,EAES,YACR,aACA,QACa;AACb,WAAO,KAAK,OAAO,MAAM,OAAO,WAAW;AAC1C,YAAM,UAAU,IAAI;AAAA,QACnB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AACA,YAAM,KAAK,IAAI,sBAAsB,KAAK,SAAS,SAAS,KAAK,MAAM;AACvE,UAAI,QAAQ;AACX,cAAM,GAAG,eAAe,MAAM;AAAA,MAC/B;AACA,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,8BAGH,cAA8D;AAAA,EAGvE,YACC,SAEkB,SAClB,QACA,cAAc,GACb;AACD,UAAM,SAAS,SAAS,QAAQ,WAAW;AAJzB;AAAA,EAKnB;AAAA,EAVA,QAAiB,UAAU,IAAY;AAAA,EAY9B,YACR,aACa;AACb,WAAO,KAAK,QAAQ,OAAO,UAAU,CAAC,WAAW;AAChD,YAAM,UAAU,IAAI;AAAA,QACnB;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ;AAAA,MACd;AACA,YAAM,KAAK,IAAI,sBAA4C,KAAK,SAAS,SAAS,KAAK,MAAM;AAC7F,aAAO,YAAY,EAAE;AAAA,IACtB,CAAC;AAAA,EACF;AACD;", "names": []}