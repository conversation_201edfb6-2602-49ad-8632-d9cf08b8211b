{"version": 3, "sources": ["../../../src/sqlite-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport type { Equal, Writable } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = SQLiteTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteText';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tgenerated: undefined;\n}>;\n\nexport class SQLiteTextBuilder<T extends ColumnBuilderBaseConfig<'string', 'SQLiteText'>> extends SQLiteColumnBuilder<\n\tT,\n\t{ length: number | undefined; enumValues: T['enumValues'] }\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteTextBuilder';\n\n\tconstructor(name: T['name'], config: SQLiteTextConfig<'text', T['enumValues']>) {\n\t\tsuper(name, 'string', 'SQLiteText');\n\t\tthis.config.enumValues = config.enum;\n\t\tthis.config.length = config.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class SQLiteText<T extends ColumnBaseConfig<'string', 'SQLiteText'>>\n\textends SQLiteColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'SQLiteText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\treadonly length: number | undefined = this.config.length;\n\n\tconstructor(\n\t\ttable: AnySQLiteTable<{ name: T['tableName'] }>,\n\t\tconfig: SQLiteTextBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `text${this.config.length ? `(${this.config.length})` : ''}`;\n\t}\n}\n\nexport type SQLiteTextJsonBuilderInitial<TName extends string> = SQLiteTextJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SQLiteTextJson';\n\tdata: unknown;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SQLiteTextJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'SQLiteTextJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SQLiteTextJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteTextJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteTextJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteTextJson<T extends ColumnBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'SQLiteTextJson';\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n\n\toverride mapFromDriverValue(value: string): T['data'] {\n\t\treturn JSON.parse(value);\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n}\n\nexport type SQLiteTextConfig<\n\tTMode extends 'text' | 'json',\n\tTEnum extends readonly string[] | string[] | undefined,\n> = TMode extends 'text' ? {\n\t\tmode?: TMode;\n\t\tlength?: number;\n\t\tenum?: TEnum;\n\t}\n\t: {\n\t\tmode?: TMode;\n\t};\n\nexport function text<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n>(\n\tname: TName,\n\tconfig: SQLiteTextConfig<TMode, T | Writable<T>> = {} as SQLiteTextConfig<TMode, T | Writable<T>>,\n): Equal<TMode, 'json'> extends true ? SQLiteTextJsonBuilderInitial<TName>\n\t: SQLiteTextBuilderInitial<TName, Writable<T>>\n{\n\treturn (config.mode === 'json'\n\t\t? new SQLiteTextJsonBuilder(name)\n\t\t: new SQLiteTextBuilder(name, config as SQLiteTextConfig<'text', Writable<T>>)) as Equal<TMode, 'json'> extends true\n\t\t\t? SQLiteTextJsonBuilderInitial<TName>\n\t\t\t: SQLiteTextBuilderInitial<TName, Writable<T>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAkD;AAY3C,MAAM,0BAAqF,kCAGhG;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAmD;AAC/E,UAAM,MAAM,UAAU,YAAY;AAClC,SAAK,OAAO,aAAa,OAAO;AAChC,SAAK,OAAO,SAAS,OAAO;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBACJ,2BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAErB,aAAa,KAAK,OAAO;AAAA,EAElC,SAA6B,KAAK,OAAO;AAAA,EAElD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO,OAAO,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,MAAM,MAAM,EAAE;AAAA,EAClE;AACD;AAYO,MAAM,8BACJ,kCACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,gBAAgB;AAAA,EACrC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,2BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAA0B;AACrD,WAAO,KAAK,MAAM,KAAK;AAAA,EACxB;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AACD;AAcO,SAAS,KAMf,MACA,SAAmD,CAAC,GAGrD;AACC,SAAQ,OAAO,SAAS,SACrB,IAAI,sBAAsB,IAAI,IAC9B,IAAI,kBAAkB,MAAM,MAA+C;AAG/E;", "names": []}