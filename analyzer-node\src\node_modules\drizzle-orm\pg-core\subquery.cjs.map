{"version": 3, "sources": ["../../src/pg-core/subquery.ts"], "sourcesContent": ["import type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport type { Subquery, WithSubquery } from '~/subquery.ts';\n\nexport type SubqueryWithSelection<TSelection extends ColumnsSelection, T<PERSON><PERSON>s extends string> =\n\t& Subquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'pg'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'pg'>;\n\nexport type WithSubqueryWithSelection<TSelection extends ColumnsSelection, <PERSON><PERSON><PERSON><PERSON> extends string> =\n\t& WithSubquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'pg'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'pg'>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}