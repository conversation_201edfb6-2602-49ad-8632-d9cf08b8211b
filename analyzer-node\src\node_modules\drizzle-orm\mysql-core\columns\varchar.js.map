{"version": 3, "sources": ["../../../src/mysql-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlVarCharBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = MySqlVarCharBuilder<\n\t{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'MySqlVarChar';\n\t\tdata: TEnum[number];\n\t\tdriverParam: number | string;\n\t\tenumValues: TEnum;\n\t\tgenerated: undefined;\n\t}\n>;\n\nexport class MySqlVarCharBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlVarChar'>>\n\textends MySqlColumnBuilder<T, MySqlVarCharConfig<T['enumValues']>>\n{\n\tstatic readonly [entityKind]: string = 'MySqlVarCharBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: MySqlVarCharConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'MySqlVarChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlVarChar<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new MySqlVarChar<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlVarChar<T extends ColumnBaseConfig<'string', 'MySqlVarChar'>>\n\textends MySqlColumn<T, MySqlVarCharConfig<T['enumValues']>>\n{\n\tstatic readonly [entityKind]: string = 'MySqlVarChar';\n\n\treadonly length: number | undefined = this.config.length;\n\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface MySqlVarCharConfig<TEnum extends string[] | readonly string[] | undefined> {\n\tlength: number;\n\tenum?: TEnum;\n}\n\nexport function varchar<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlVarCharConfig<T | Writable<T>>,\n): MySqlVarCharBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlVarCharBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAS,aAAa,0BAA0B;AAczC,MAAM,4BACJ,mBACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC,YAAY,MAAiB,QAA6C;AACzE,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACkF;AAClF,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,YACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAE9B,SAA6B,KAAK,OAAO;AAAA,EAEhC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;AAAA,EACtE;AACD;AAOO,SAAS,QACf,MACA,QACiD;AACjD,SAAO,IAAI,oBAAoB,MAAM,MAAM;AAC5C;", "names": []}