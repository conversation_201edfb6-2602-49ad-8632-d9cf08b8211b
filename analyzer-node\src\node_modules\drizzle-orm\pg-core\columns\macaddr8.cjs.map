{"version": 3, "sources": ["../../../src/pg-core/columns/macaddr8.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '../table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgMacaddr8BuilderInitial<TName extends string> = PgMacaddr8Builder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgMacaddr8';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgMacaddr8Builder<T extends ColumnBuilderBaseConfig<'string', 'PgMacaddr8'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgMacaddr8Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgMacaddr8');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgMacaddr8<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgMacaddr8<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgMacaddr8<T extends ColumnBaseConfig<'string', 'PgMacaddr8'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgMacaddr8';\n\n\tgetSQLType(): string {\n\t\treturn 'macaddr8';\n\t}\n}\n\nexport function macaddr8<TName extends string>(name: TName): PgMacaddr8BuilderInitial<TName> {\n\treturn new PgMacaddr8Builder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,0BAAqF,8BAAmB;AAAA,EACpH,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,uBAAY;AAAA,EAC/F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,SAA+B,MAA8C;AAC5F,SAAO,IAAI,kBAAkB,IAAI;AAClC;", "names": []}