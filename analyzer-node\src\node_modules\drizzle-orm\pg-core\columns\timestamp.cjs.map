{"version": 3, "sources": ["../../../src/pg-core/columns/timestamp.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Equal } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\n\nexport type PgTimestampBuilderInitial<TName extends string> = PgTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'PgTimestamp';\n\tdata: Date;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'PgTimestamp'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic readonly [entityKind]: string = 'PgTimestampBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'date', 'PgTimestamp');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestamp<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTimestamp<T extends ColumnBaseConfig<'date', 'PgTimestamp'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgTimestamp';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : ` (${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n\n\toverride mapFromDriverValue = (value: string): Date | null => {\n\t\treturn new Date(this.withTimezone ? value : value + '+0000');\n\t};\n\n\toverride mapToDriverValue = (value: Date): string => {\n\t\treturn value.toISOString();\n\t};\n}\n\nexport type PgTimestampStringBuilderInitial<TName extends string> = PgTimestampStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTimestampString';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgTimestampStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTimestampString'>>\n\textends PgDateColumnBaseBuilder<\n\t\tT,\n\t\t{ withTimezone: boolean; precision: number | undefined }\n\t>\n{\n\tstatic readonly [entityKind]: string = 'PgTimestampStringBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\twithTimezone: boolean,\n\t\tprecision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTimestampString');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTimestampString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTimestampString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgTimestampString<T extends ColumnBaseConfig<'string', 'PgTimestampString'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgTimestampString';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimestampStringBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `timestamp${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport type Precision = 0 | 1 | 2 | 3 | 4 | 5 | 6;\n\nexport interface PgTimestampConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function timestamp<TName extends string, TMode extends PgTimestampConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: PgTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? PgTimestampStringBuilderInitial<TName> : PgTimestampBuilderInitial<TName>;\nexport function timestamp(\n\tname: string,\n\tconfig: PgTimestampConfig = {},\n) {\n\tif (config.mode === 'string') {\n\t\treturn new PgTimestampStringBuilder(name, config.withTimezone ?? false, config.precision);\n\t}\n\treturn new PgTimestampBuilder(name, config.withTimezone ?? false, config.precision);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAyB;AACzB,yBAAwC;AAYjC,MAAM,2BACJ,2CAIT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YACC,MACA,cACA,WACC;AACD,UAAM,MAAM,QAAQ,aAAa;AACjC,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;AAAA,EACzB;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAAuE,uBAAY;AAAA,EAC/F,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAAyC;AACjG,UAAM,OAAO,MAAM;AACnB,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;AAAA,EACzB;AAAA,EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,KAAK,KAAK,SAAS;AACzE,WAAO,YAAY,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;AAAA,EAC1E;AAAA,EAES,qBAAqB,CAAC,UAA+B;AAC7D,WAAO,IAAI,KAAK,KAAK,eAAe,QAAQ,QAAQ,OAAO;AAAA,EAC5D;AAAA,EAES,mBAAmB,CAAC,UAAwB;AACpD,WAAO,MAAM,YAAY;AAAA,EAC1B;AACD;AAYO,MAAM,iCACJ,2CAIT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YACC,MACA,cACA,WACC;AACD,UAAM,MAAM,UAAU,mBAAmB;AACzC,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;AAAA,EACzB;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,0BAAqF,uBAAY;AAAA,EAC7G,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAA+C;AACvG,UAAM,OAAO,MAAM;AACnB,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;AAAA,EACzB;AAAA,EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,IAAI,KAAK,SAAS;AACxE,WAAO,YAAY,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;AAAA,EAC1E;AACD;AAcO,SAAS,UACf,MACA,SAA4B,CAAC,GAC5B;AACD,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,yBAAyB,MAAM,OAAO,gBAAgB,OAAO,OAAO,SAAS;AAAA,EACzF;AACA,SAAO,IAAI,mBAAmB,MAAM,OAAO,gBAAgB,OAAO,OAAO,SAAS;AACnF;", "names": []}