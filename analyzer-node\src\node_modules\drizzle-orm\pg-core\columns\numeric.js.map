{"version": 3, "sources": ["../../../src/pg-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgNumericBuilderInitial<TName extends string> = PgNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgNumeric'>> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tprecision: number | undefined;\n\t\tscale: number | undefined;\n\t}\n> {\n\tstatic readonly [entityKind]: string = 'PgNumericBuilder';\n\n\tconstructor(name: string, precision?: number, scale?: number) {\n\t\tsuper(name, 'string', 'PgNumeric');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgNumeric<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgNumeric<T extends ColumnBaseConfig<'string', 'PgNumeric'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgNumeric';\n\n\treadonly precision: number | undefined;\n\treadonly scale: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgNumericBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.precision = config.precision;\n\t\tthis.scale = config.scale;\n\t}\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `numeric(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'numeric';\n\t\t} else {\n\t\t\treturn `numeric(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport function numeric<TName extends string>(\n\tname: TName,\n\tconfig?:\n\t\t| { precision: number; scale?: number }\n\t\t| { precision?: number; scale: number }\n\t\t| { precision: number; scale: number },\n): PgNumericBuilderInitial<TName> {\n\treturn new PgNumericBuilder(name, config?.precision, config?.scale);\n}\n\nexport const decimal = numeric;\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;AAYnC,MAAM,yBAAmF,gBAM9F;AAAA,EACD,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,WAAoB,OAAgB;AAC7D,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAqE,SAAY;AAAA,EAC7F,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAAuC;AAC/F,UAAM,OAAO,MAAM;AACnB,SAAK,YAAY,OAAO;AACxB,SAAK,QAAQ,OAAO;AAAA,EACrB;AAAA,EAEA,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;AAAA,IACjC;AAAA,EACD;AACD;AAEO,SAAS,QACf,MACA,QAIiC;AACjC,SAAO,IAAI,iBAAiB,MAAM,QAAQ,WAAW,QAAQ,KAAK;AACnE;AAEO,MAAM,UAAU;", "names": []}